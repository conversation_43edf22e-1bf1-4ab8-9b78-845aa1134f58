/**
 * @file task_system.h
 * @brief TIMO任务管理系统头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef TASK_SYSTEM_H
#define TASK_SYSTEM_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 任务状态定义 */
typedef enum {
    TASK_STATUS_PENDING = 0,    // 待办
    TASK_STATUS_IN_PROGRESS,    // 进行中
    TASK_STATUS_COMPLETED,      // 已完成
    TASK_STATUS_CANCELLED,      // 已取消
    TASK_STATUS_OVERDUE,        // 已过期
    TASK_STATUS_MAX
} task_status_t;

/* 任务优先级定义 */
typedef enum {
    TASK_PRIORITY_LOW = 0,      // 低优先级
    TASK_PRIORITY_NORMAL,       // 普通优先级
    TASK_PRIORITY_HIGH,         // 高优先级
    TASK_PRIORITY_URGENT,       // 紧急
    TASK_PRIORITY_MAX
} task_priority_t;

/* 任务类型定义 */
typedef enum {
    TASK_TYPE_GENERAL = 0,      // 一般任务
    TASK_TYPE_WORK,             // 工作任务
    TASK_TYPE_STUDY,            // 学习任务
    TASK_TYPE_LIFE,             // 生活任务
    TASK_TYPE_HEALTH,           // 健康任务
    TASK_TYPE_ENTERTAINMENT,    // 娱乐任务
    TASK_TYPE_MAX
} task_type_t;

/* 提醒类型定义 */
typedef enum {
    REMINDER_TYPE_NONE = 0,     // 无提醒
    REMINDER_TYPE_SOUND,        // 声音提醒
    REMINDER_TYPE_VIBRATION,    // 振动提醒
    REMINDER_TYPE_LIGHT,        // 灯光提醒
    REMINDER_TYPE_ALL,          // 全部提醒
    REMINDER_TYPE_MAX
} reminder_type_t;

/* 任务信息 */
typedef struct {
    uint32_t id;                // 任务ID
    char title[64];             // 任务标题
    char description[256];      // 任务描述
    task_type_t type;           // 任务类型
    task_priority_t priority;   // 优先级
    task_status_t status;       // 状态
    time_t created_time;        // 创建时间
    time_t due_time;            // 截止时间
    time_t completed_time;      // 完成时间
    time_t reminder_time;       // 提醒时间
    reminder_type_t reminder_type; // 提醒类型
    uint32_t estimated_minutes; // 预估时间(分钟)
    uint32_t actual_minutes;    // 实际时间(分钟)
    uint8_t progress;           // 进度(0-100)
    bool is_recurring;          // 是否重复
    uint32_t recurrence_interval; // 重复间隔(天)
    char tags[128];             // 标签
} task_info_t;

/* 番茄时钟状态 */
typedef enum {
    POMODORO_STATE_IDLE = 0,    // 空闲
    POMODORO_STATE_WORK,        // 工作中
    POMODORO_STATE_SHORT_BREAK, // 短休息
    POMODORO_STATE_LONG_BREAK,  // 长休息
    POMODORO_STATE_PAUSED,      // 暂停
    POMODORO_STATE_MAX
} pomodoro_state_t;

/* 番茄时钟配置 */
typedef struct {
    uint32_t work_duration_min;     // 工作时长(分钟)
    uint32_t short_break_min;       // 短休息时长(分钟)
    uint32_t long_break_min;        // 长休息时长(分钟)
    uint8_t cycles_before_long_break; // 长休息前的周期数
    bool auto_start_breaks;         // 自动开始休息
    bool auto_start_work;           // 自动开始工作
    bool sound_enabled;             // 声音提醒
    bool vibration_enabled;         // 振动提醒
    uint8_t focus_level;            // 专注等级(1-10)
} pomodoro_config_t;

/* 番茄时钟信息 */
typedef struct {
    pomodoro_state_t state;         // 当前状态
    uint32_t current_task_id;       // 当前任务ID
    uint32_t remaining_seconds;     // 剩余秒数
    uint32_t total_seconds;         // 总秒数
    uint8_t current_cycle;          // 当前周期
    uint32_t total_work_time;       // 总工作时间
    uint32_t total_break_time;      // 总休息时间
    uint32_t completed_cycles;      // 完成的周期数
    time_t session_start_time;      // 会话开始时间
} pomodoro_info_t;

/* 任务统计信息 */
typedef struct {
    uint32_t total_tasks;           // 总任务数
    uint32_t completed_tasks;       // 已完成任务数
    uint32_t pending_tasks;         // 待办任务数
    uint32_t overdue_tasks;         // 过期任务数
    uint32_t total_work_time;       // 总工作时间(分钟)
    uint32_t avg_completion_time;   // 平均完成时间(分钟)
    float completion_rate;          // 完成率
    uint32_t productivity_score;    // 生产力评分
} task_statistics_t;

/* 任务事件类型 */
typedef enum {
    TASK_EVENT_CREATED = 0,         // 任务创建
    TASK_EVENT_UPDATED,             // 任务更新
    TASK_EVENT_COMPLETED,           // 任务完成
    TASK_EVENT_DELETED,             // 任务删除
    TASK_EVENT_REMINDER,            // 任务提醒
    TASK_EVENT_OVERDUE,             // 任务过期
    TASK_EVENT_POMODORO_START,      // 番茄时钟开始
    TASK_EVENT_POMODORO_PAUSE,      // 番茄时钟暂停
    TASK_EVENT_POMODORO_COMPLETE,   // 番茄时钟完成
    TASK_EVENT_BREAK_START,         // 休息开始
    TASK_EVENT_BREAK_END,           // 休息结束
    TASK_EVENT_MAX
} task_event_type_t;

/* 任务事件数据 */
typedef struct {
    task_event_type_t type;         // 事件类型
    uint32_t task_id;               // 任务ID
    time_t timestamp;               // 时间戳
    union {
        task_status_t new_status;   // 新状态
        pomodoro_state_t pomodoro_state; // 番茄时钟状态
        uint32_t work_time;         // 工作时间
    } data;
} task_event_t;

/* 任务系统配置 */
typedef struct {
    bool auto_save;                 // 自动保存
    uint32_t save_interval_s;       // 保存间隔(秒)
    bool cloud_sync;                // 云端同步
    bool reminder_enabled;          // 提醒开启
    uint32_t max_tasks;             // 最大任务数
    pomodoro_config_t pomodoro;     // 番茄时钟配置
} task_system_config_t;

/* 任务系统回调函数 */
typedef void (*task_event_callback_t)(const task_event_t *event);

/* 任务系统API */

/**
 * @brief 初始化任务系统
 */
esp_err_t task_system_init(void);

/**
 * @brief 启动任务系统
 */
esp_err_t task_system_start(void);

/**
 * @brief 停止任务系统
 */
esp_err_t task_system_stop(void);

/**
 * @brief 反初始化任务系统
 */
esp_err_t task_system_deinit(void);

/**
 * @brief 设置任务系统配置
 */
esp_err_t task_system_set_config(const task_system_config_t *config);

/**
 * @brief 获取任务系统配置
 */
esp_err_t task_system_get_config(task_system_config_t *config);

/**
 * @brief 注册事件回调
 */
esp_err_t task_system_register_event_callback(task_event_callback_t callback);

/**
 * @brief 创建新任务
 */
esp_err_t task_system_create_task(const task_info_t *task_info, uint32_t *task_id);

/**
 * @brief 更新任务
 */
esp_err_t task_system_update_task(uint32_t task_id, const task_info_t *task_info);

/**
 * @brief 删除任务
 */
esp_err_t task_system_delete_task(uint32_t task_id);

/**
 * @brief 获取任务信息
 */
esp_err_t task_system_get_task(uint32_t task_id, task_info_t *task_info);

/**
 * @brief 完成任务
 */
esp_err_t task_system_complete_task(uint32_t task_id);

/**
 * @brief 获取任务列表
 */
esp_err_t task_system_get_task_list(task_info_t *tasks, uint32_t *count, uint32_t max_count);

/**
 * @brief 获取待办任务列表
 */
esp_err_t task_system_get_pending_tasks(task_info_t *tasks, uint32_t *count, uint32_t max_count);

/**
 * @brief 获取今日任务列表
 */
esp_err_t task_system_get_today_tasks(task_info_t *tasks, uint32_t *count, uint32_t max_count);

/**
 * @brief 搜索任务
 */
esp_err_t task_system_search_tasks(const char *keyword, task_info_t *tasks, uint32_t *count, uint32_t max_count);

/**
 * @brief 获取任务统计信息
 */
esp_err_t task_system_get_statistics(task_statistics_t *stats);

/* 番茄时钟API */

/**
 * @brief 开始番茄时钟
 */
esp_err_t pomodoro_start(uint32_t task_id);

/**
 * @brief 暂停番茄时钟
 */
esp_err_t pomodoro_pause(void);

/**
 * @brief 恢复番茄时钟
 */
esp_err_t pomodoro_resume(void);

/**
 * @brief 停止番茄时钟
 */
esp_err_t pomodoro_stop(void);

/**
 * @brief 获取番茄时钟信息
 */
esp_err_t pomodoro_get_info(pomodoro_info_t *info);

/**
 * @brief 设置番茄时钟配置
 */
esp_err_t pomodoro_set_config(const pomodoro_config_t *config);

/**
 * @brief 获取番茄时钟配置
 */
esp_err_t pomodoro_get_config(pomodoro_config_t *config);

/**
 * @brief 保存任务数据
 */
esp_err_t task_system_save_data(void);

/**
 * @brief 加载任务数据
 */
esp_err_t task_system_load_data(void);

#ifdef __cplusplus
}
#endif

#endif // TASK_SYSTEM_H
