/**
 * @file ui_pomodoro.c
 * @brief TIMO番茄时钟UI界面实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "ui_pomodoro.h"
#include "ui_manager.h"
#include "esp_log.h"
#include "lvgl.h"

static const char *TAG = "UI_POMODORO";

/* UI对象 */
static lv_obj_t *g_pomodoro_page = NULL;

/**
 * @brief 初始化番茄时钟UI
 */
esp_err_t ui_pomodoro_init(void)
{
    ESP_LOGI(TAG, "初始化番茄时钟UI...");
    
    // 创建番茄时钟页面
    g_pomodoro_page = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(g_pomodoro_page, lv_color_hex(0x000000), 0);
    
    // 创建标题
    lv_obj_t *title_label = lv_label_create(g_pomodoro_page);
    lv_obj_set_style_text_color(title_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_18, 0);
    lv_obj_align(title_label, LV_ALIGN_TOP_MID, 0, 20);
    lv_label_set_text(title_label, "番茄时钟");
    
    ESP_LOGI(TAG, "番茄时钟UI初始化完成");
    return ESP_OK;
}

/**
 * @brief 反初始化番茄时钟UI
 */
esp_err_t ui_pomodoro_deinit(void)
{
    if (g_pomodoro_page) {
        lv_obj_del(g_pomodoro_page);
        g_pomodoro_page = NULL;
    }
    
    ESP_LOGI(TAG, "番茄时钟UI反初始化完成");
    return ESP_OK;
}

/**
 * @brief 显示番茄时钟页面
 */
void ui_pomodoro_show_page(void)
{
    if (g_pomodoro_page) {
        lv_obj_clear_flag(g_pomodoro_page, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief 隐藏番茄时钟页面
 */
void ui_pomodoro_hide_page(void)
{
    if (g_pomodoro_page) {
        lv_obj_add_flag(g_pomodoro_page, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief 更新番茄时钟显示
 */
void ui_pomodoro_update_display(const pomodoro_info_t *info)
{
    if (!info) {
        return;
    }
    
    ESP_LOGI(TAG, "更新番茄时钟显示");
    // TODO: 实现番茄时钟显示更新
}

/**
 * @brief 显示番茄时钟设置
 */
void ui_pomodoro_show_settings(void)
{
    ESP_LOGI(TAG, "显示番茄时钟设置");
    // TODO: 实现番茄时钟设置界面
}
