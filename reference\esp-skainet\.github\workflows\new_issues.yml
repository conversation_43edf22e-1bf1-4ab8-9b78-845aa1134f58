name: Sync issues to <PERSON><PERSON>

# This workflow will be triggered when a new issue is opened
on: issues

jobs:
  sync_issues_to_jira:
    name: Sync issues to <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
      - name: Sync GitHub issues to <PERSON>ra project
        uses: espressif/github-actions/sync_issues_to_jira@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          JIRA_PASS: ${{ secrets.JIRA_PASS }}
          JIRA_PROJECT: AIS
          JIRA_URL: ${{ secrets.JIRA_URL }}
          JIRA_USER: ${{ secrets.JIRA_USER }}
