# Wake Word Detection

## `wake_word_detection/wakenet`

| Supported Targets | ESP32    | ESP32-S2 | ESP32-S3 | ESP32-P4 | ESP32-C3 | ESP32-C5 | ESP32-C6 | 
| ----------------- | -------- | -------- | -------- | -------- | -------- | -------- | -------- |

This example shows how to use the Wakenet interface directly. If you are using a single microphone and only need to use WakeNet, and you want to reduce memory and CPU resource consumption, this example is recommended.


## `wake_word_detection/afe`

| Supported Targets | ESP32    | ESP32-S3 | ESP32-P4 | 
| ----------------- | -------- | -------- | -------- |

This example shows how to use WakeNet through the AFE interface. If you are using a dual-microphone or require more speech enhancement algorithms, this example is recommended.
