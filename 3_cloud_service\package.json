{
  "name": "timo-cloud-service",
  "version": "1.0.0",
  "description": "TIMO智能闹钟云端服务",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/",
    "docker:build": "docker build -t timo-cloud-service .",
    "docker:run": "docker run -p 3000:3000 timo-cloud-service"
  },
  "keywords": [
    "timo",
    "smart-clock",
    "iot",
    "cloud-service",
    "api"
  ],
  "author": "TIMO Team",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "mongoose": "^7.5.0",
    "redis": "^4.6.7",
    "jsonwebtoken": "^9.0.2",
    "bcryptjs": "^2.4.3",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.8.1",
    "winston": "^3.10.0",
    "dotenv": "^16.3.1",
    "joi": "^17.9.2",
    "multer": "^1.4.5-lts.1",
    "socket.io": "^4.7.2",
    "node-cron": "^3.0.2",
    "nodemailer": "^6.9.4",
    "axios": "^1.5.0",
    "mqtt": "^5.0.0",
    "form-data": "^4.0.0",
    "ws": "^8.13.0",
    "cron": "^2.4.4",
    "sharp": "^0.32.5",
    "archiver": "^6.0.1",
    "unzipper": "^0.10.14"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.6.2",
    "supertest": "^6.3.3",
    "eslint": "^8.47.0",
    "prettier": "^3.0.2",
    "@types/node": "^20.5.0"
  },
  "engines": {
    "node": ">=16.0.0",
    "npm": ">=8.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/timo-team/timo-cloud-service.git"
  },
  "bugs": {
    "url": "https://github.com/timo-team/timo-cloud-service/issues"
  },
  "homepage": "https://github.com/timo-team/timo-cloud-service#readme"
    "supertest": "^6.3.3",
    "eslint": "^8.47.0",
    "prettier": "^3.0.1",
    "@types/node": "^20.5.0"
  },
  "engines": {
    "node": ">=16.0.0",
    "npm": ">=8.0.0"
  }
}
