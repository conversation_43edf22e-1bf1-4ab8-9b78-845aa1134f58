/**
 * @file task_system.c
 * @brief TIMO任务管理系统实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "task_system.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <string.h>
#include <time.h>

static const char *TAG = "TASK_SYSTEM";

/* 系统状态 */
static bool g_task_system_initialized = false;
static bool g_task_system_running = false;

/* 任务和同步 */
static TaskHandle_t g_task_manager_handle = NULL;
static SemaphoreHandle_t g_task_mutex = NULL;
static QueueHandle_t g_task_event_queue = NULL;
static esp_timer_handle_t g_task_check_timer = NULL;

/* 任务数据 */
static task_info_t g_tasks[32];  // 最多32个任务
static uint32_t g_task_count = 0;
static uint32_t g_next_task_id = 1;

/* 配置和回调 */
static task_system_config_t g_task_config;
static task_event_callback_t g_event_callback = NULL;

/* 默认配置 */
static const task_system_config_t DEFAULT_TASK_CONFIG = {
    .auto_save = true,
    .save_interval_s = 300,  // 5分钟
    .cloud_sync = false,
    .reminder_enabled = true,
    .max_tasks = 32,
    .pomodoro = {
        .work_duration_min = 25,
        .short_break_min = 5,
        .long_break_min = 15,
        .cycles_before_long_break = 4,
        .auto_start_breaks = false,
        .auto_start_work = false,
        .sound_enabled = true,
        .vibration_enabled = false,
        .focus_level = 5
    }
};

/* 前向声明 */
static void task_manager_task(void *pvParameters);
static void task_check_timer_callback(void *arg);
static esp_err_t save_task_to_nvs(const task_info_t *task);
static esp_err_t load_task_from_nvs(uint32_t task_id, task_info_t *task);
static void send_task_event(task_event_type_t type, uint32_t task_id, const void *data);
static void check_task_reminders(void);

/**
 * @brief 初始化任务系统
 */
esp_err_t task_system_init(void)
{
    if (g_task_system_initialized) {
        ESP_LOGW(TAG, "任务系统已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化任务系统...");

    // 创建互斥锁
    g_task_mutex = xSemaphoreCreateMutex();
    if (!g_task_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建事件队列
    g_task_event_queue = xQueueCreate(10, sizeof(task_event_t));
    if (!g_task_event_queue) {
        ESP_LOGE(TAG, "创建事件队列失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建检查定时器
    esp_timer_create_args_t timer_args = {
        .callback = task_check_timer_callback,
        .name = "task_check"
    };
    esp_err_t ret = esp_timer_create(&timer_args, &g_task_check_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建检查定时器失败");
        return ret;
    }

    // 初始化配置
    g_task_config = DEFAULT_TASK_CONFIG;

    // 初始化任务数据
    memset(g_tasks, 0, sizeof(g_tasks));
    g_task_count = 0;

    g_task_system_initialized = true;
    ESP_LOGI(TAG, "任务系统初始化完成");

    return ESP_OK;
}

/**
 * @brief 启动任务系统
 */
esp_err_t task_system_start(void)
{
    if (!g_task_system_initialized) {
        ESP_LOGE(TAG, "任务系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_task_system_running) {
        ESP_LOGW(TAG, "任务系统已启动");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "启动任务系统...");

    // 加载任务数据
    task_system_load_data();

    // 创建任务管理任务
    BaseType_t ret = xTaskCreate(
        task_manager_task,
        "task_manager",
        4096,
        NULL,
        5,
        &g_task_manager_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建任务管理任务失败");
        return ESP_ERR_NO_MEM;
    }

    // 启动检查定时器
    esp_err_t timer_ret = esp_timer_start_periodic(g_task_check_timer, 60000000); // 1分钟
    if (timer_ret != ESP_OK) {
        ESP_LOGW(TAG, "启动检查定时器失败");
    }

    g_task_system_running = true;
    ESP_LOGI(TAG, "任务系统启动完成");

    return ESP_OK;
}

/**
 * @brief 停止任务系统
 */
esp_err_t task_system_stop(void)
{
    if (!g_task_system_running) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止任务系统...");

    g_task_system_running = false;

    // 停止定时器
    if (g_task_check_timer) {
        esp_timer_stop(g_task_check_timer);
    }

    // 保存任务数据
    task_system_save_data();

    ESP_LOGI(TAG, "任务系统停止完成");
    return ESP_OK;
}

/**
 * @brief 创建新任务
 */
esp_err_t task_system_create_task(const task_info_t *task_info, uint32_t *task_id)
{
    if (!task_info || !task_id) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_task_mutex, portMAX_DELAY);

    if (g_task_count >= g_task_config.max_tasks) {
        xSemaphoreGive(g_task_mutex);
        ESP_LOGE(TAG, "任务数量已达上限");
        return ESP_ERR_NO_MEM;
    }

    // 创建新任务
    task_info_t *task = &g_tasks[g_task_count];
    *task = *task_info;
    task->id = g_next_task_id++;
    task->created_time = time(NULL);
    task->status = TASK_STATUS_PENDING;

    g_task_count++;
    *task_id = task->id;

    xSemaphoreGive(g_task_mutex);

    // 保存到NVS
    save_task_to_nvs(task);

    // 发送创建事件
    send_task_event(TASK_EVENT_CREATED, task->id, NULL);

    ESP_LOGI(TAG, "创建任务成功: ID=%d, 标题=%s", task->id, task->title);
    return ESP_OK;
}

/**
 * @brief 完成任务
 */
esp_err_t task_system_complete_task(uint32_t task_id)
{
    xSemaphoreTake(g_task_mutex, portMAX_DELAY);

    for (uint32_t i = 0; i < g_task_count; i++) {
        if (g_tasks[i].id == task_id) {
            g_tasks[i].status = TASK_STATUS_COMPLETED;
            g_tasks[i].completed_time = time(NULL);
            g_tasks[i].progress = 100;

            xSemaphoreGive(g_task_mutex);

            // 保存数据
            save_task_to_nvs(&g_tasks[i]);

            // 发送完成事件
            send_task_event(TASK_EVENT_COMPLETED, task_id, NULL);

            ESP_LOGI(TAG, "完成任务: ID=%d", task_id);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_task_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 获取任务信息
 */
esp_err_t task_system_get_task(uint32_t task_id, task_info_t *task_info)
{
    if (!task_info) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_task_mutex, portMAX_DELAY);

    for (uint32_t i = 0; i < g_task_count; i++) {
        if (g_tasks[i].id == task_id) {
            *task_info = g_tasks[i];
            xSemaphoreGive(g_task_mutex);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_task_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 获取待办任务列表
 */
esp_err_t task_system_get_pending_tasks(task_info_t *tasks, uint32_t *count, uint32_t max_count)
{
    if (!tasks || !count) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_task_mutex, portMAX_DELAY);

    uint32_t pending_count = 0;
    for (uint32_t i = 0; i < g_task_count && pending_count < max_count; i++) {
        if (g_tasks[i].status == TASK_STATUS_PENDING) {
            tasks[pending_count] = g_tasks[i];
            pending_count++;
        }
    }

    *count = pending_count;
    xSemaphoreGive(g_task_mutex);

    return ESP_OK;
}

/**
 * @brief 任务管理任务
 */
static void task_manager_task(void *pvParameters)
{
    ESP_LOGI(TAG, "任务管理任务启动");
    
    task_event_t event;
    
    while (g_task_system_running) {
        // 处理事件队列
        if (xQueueReceive(g_task_event_queue, &event, pdMS_TO_TICKS(1000)) == pdTRUE) {
            // 调用事件回调
            if (g_event_callback) {
                g_event_callback(&event);
            }
            
            ESP_LOGD(TAG, "处理任务事件: 类型=%d, 任务ID=%d", event.type, event.task_id);
        }
    }
    
    ESP_LOGI(TAG, "任务管理任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 检查定时器回调
 */
static void task_check_timer_callback(void *arg)
{
    if (!g_task_system_running) {
        return;
    }
    
    check_task_reminders();
}

/**
 * @brief 检查任务提醒
 */
static void check_task_reminders(void)
{
    time_t now = time(NULL);
    
    xSemaphoreTake(g_task_mutex, portMAX_DELAY);
    
    for (uint32_t i = 0; i < g_task_count; i++) {
        task_info_t *task = &g_tasks[i];
        
        if (task->status == TASK_STATUS_PENDING) {
            // 检查提醒时间
            if (task->reminder_time > 0 && now >= task->reminder_time) {
                send_task_event(TASK_EVENT_REMINDER, task->id, NULL);
                task->reminder_time = 0; // 避免重复提醒
            }
            
            // 检查过期时间
            if (task->due_time > 0 && now > task->due_time) {
                task->status = TASK_STATUS_OVERDUE;
                send_task_event(TASK_EVENT_OVERDUE, task->id, NULL);
            }
        }
    }
    
    xSemaphoreGive(g_task_mutex);
}

/**
 * @brief 发送任务事件
 */
static void send_task_event(task_event_type_t type, uint32_t task_id, const void *data)
{
    task_event_t event = {
        .type = type,
        .task_id = task_id,
        .timestamp = time(NULL)
    };
    
    if (data) {
        memcpy(&event.data, data, sizeof(event.data));
    }
    
    xQueueSend(g_task_event_queue, &event, 0);
}

/**
 * @brief 保存任务数据到NVS
 */
static esp_err_t save_task_to_nvs(const task_info_t *task)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("task_data", NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        return ret;
    }
    
    char key[16];
    snprintf(key, sizeof(key), "task_%d", task->id);
    
    ret = nvs_set_blob(nvs_handle, key, task, sizeof(task_info_t));
    if (ret == ESP_OK) {
        ret = nvs_commit(nvs_handle);
    }
    
    nvs_close(nvs_handle);
    return ret;
}

/**
 * @brief 保存任务数据
 */
esp_err_t task_system_save_data(void)
{
    ESP_LOGI(TAG, "保存任务数据...");
    
    xSemaphoreTake(g_task_mutex, portMAX_DELAY);
    
    for (uint32_t i = 0; i < g_task_count; i++) {
        save_task_to_nvs(&g_tasks[i]);
    }
    
    // 保存系统状态
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("task_system", NVS_READWRITE, &nvs_handle);
    if (ret == ESP_OK) {
        nvs_set_u32(nvs_handle, "task_count", g_task_count);
        nvs_set_u32(nvs_handle, "next_id", g_next_task_id);
        nvs_commit(nvs_handle);
        nvs_close(nvs_handle);
    }
    
    xSemaphoreGive(g_task_mutex);
    
    ESP_LOGI(TAG, "任务数据保存完成");
    return ESP_OK;
}

/**
 * @brief 加载任务数据
 */
esp_err_t task_system_load_data(void)
{
    ESP_LOGI(TAG, "加载任务数据...");
    
    // 加载系统状态
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("task_system", NVS_READONLY, &nvs_handle);
    if (ret == ESP_OK) {
        size_t required_size;
        
        required_size = sizeof(uint32_t);
        nvs_get_u32(nvs_handle, "task_count", &g_task_count);
        nvs_get_u32(nvs_handle, "next_id", &g_next_task_id);
        
        nvs_close(nvs_handle);
    }
    
    // 加载任务数据
    xSemaphoreTake(g_task_mutex, portMAX_DELAY);
    
    for (uint32_t i = 0; i < g_task_count; i++) {
        // 这里需要知道任务ID，实际实现中可能需要遍历所有可能的ID
        // 简化处理，假设ID是连续的
        load_task_from_nvs(i + 1, &g_tasks[i]);
    }
    
    xSemaphoreGive(g_task_mutex);
    
    ESP_LOGI(TAG, "任务数据加载完成，任务数量: %d", g_task_count);
    return ESP_OK;
}

/**
 * @brief 从NVS加载任务数据
 */
static esp_err_t load_task_from_nvs(uint32_t task_id, task_info_t *task)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("task_data", NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        return ret;
    }
    
    char key[16];
    snprintf(key, sizeof(key), "task_%d", task_id);
    
    size_t required_size = sizeof(task_info_t);
    ret = nvs_get_blob(nvs_handle, key, task, &required_size);
    
    nvs_close(nvs_handle);
    return ret;
}
