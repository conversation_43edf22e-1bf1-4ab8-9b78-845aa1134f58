参考项目说明：

1. ESP32-S3-Simple: 立创实战派ESP32-S3-例程，里面包含ESP32-S3相关外设的示例代码，可以用来学习ESP32-S3的使用方法。
2. xiaozhi-esp32-main: 一个基于ESP32的语音交互项目，可以用来学习语音交互的实现方法。小智 AI 聊天机器人作为一个语音交互入口，利用 Qwen / DeepSeek 等大模型的 AI 能力，通过 MCP 协议实现多端控制。
3. 3D-Speaker-main: 声纹识别，识别当前说话人的身份 3D Speaker
4. ESP-Skainet 以最便捷的方式支持基于乐鑫的 ESP32系列 芯片的唤醒词识别和命令词识别应用程序的开发。使用 ESP-Skainet，您可以轻松构建唤醒词识别和命令词识别应用程序。
5. ESP-GMF 全称 Espressif General Multimedia Framework，是乐鑫开发的应用于 IoT 多媒体领域的轻量级通用软件框架。
6. ESP-Brookesia 是一个面向物联网设备的人机交互开发框架。
    - HAL：使用 ESP-IDF 提供的硬件抽象层，提供对底层硬件的访问和控制。
    - Middle：作为连接应用程序与底层硬件的桥梁，通过 Function Components 向下对接硬件抽象层，同时通过 System Services 向上为应用程序提供标准化的接口，实现系统资源的解耦与隔离。
    - Application：通过 AI Framework 提供 AI 应用场景支持，包括 HMI（单屏和双屏的拟人化交互设计）、Agent（兼容豆包、小智等主流 LLM 模型） 和 Protocol（MCP 协议实现 LLM 与系统服务统一通信）。通过 System Framework 提供各种面向产品（移动设备、音箱、机器人等）的系统和应用（设置、AI 助手、应用商店等）支持。