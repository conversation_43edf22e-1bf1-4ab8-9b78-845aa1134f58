/**
 * @file pet_system.c
 * @brief TIMO虚拟宠物系统实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "pet_system.h"
#include "pet_ai.h"
#include "pet_emotion.h"
#include "pet_interaction.h"
#include "pet_lifecycle.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <string.h>
#include <time.h>

static const char *TAG = "PET_SYSTEM";

/* 系统状态 */
static bool g_pet_system_initialized = false;
static bool g_pet_system_running = false;

/* 任务和同步 */
static TaskHandle_t g_pet_task_handle = NULL;
static SemaphoreHandle_t g_pet_mutex = NULL;
static QueueHandle_t g_pet_event_queue = NULL;
static esp_timer_handle_t g_pet_update_timer = NULL;

/* 宠物数据 */
static pet_info_t g_pets[8];  // 最多8只宠物
static uint8_t g_pet_count = 0;
static uint32_t g_active_pet_id = 0;
static uint32_t g_next_pet_id = 1;

/* 配置和回调 */
static pet_config_t g_pet_config;
static pet_event_callback_t g_event_callback = NULL;

/* 默认配置 */
static const pet_config_t DEFAULT_PET_CONFIG = {
    .auto_care = true,
    .update_interval_ms = 30000,  // 30秒
    .max_pets = 3,
    .sound_enabled = true,
    .animation_enabled = true,
    .interaction_sensitivity = 50
};

/* 前向声明 */
static void pet_system_task(void *pvParameters);
static void pet_update_timer_callback(void *arg);
static esp_err_t save_pet_to_nvs(const pet_info_t *pet);
static esp_err_t load_pet_from_nvs(uint32_t pet_id, pet_info_t *pet);
static void send_pet_event(pet_event_type_t type, uint32_t pet_id, const void *data);
static void update_pet_attributes(pet_info_t *pet);
static void check_pet_needs(pet_info_t *pet);

/**
 * @brief 初始化宠物系统
 */
esp_err_t pet_system_init(void)
{
    if (g_pet_system_initialized) {
        ESP_LOGW(TAG, "宠物系统已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化宠物系统...");

    // 创建互斥锁
    g_pet_mutex = xSemaphoreCreateMutex();
    if (!g_pet_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建事件队列
    g_pet_event_queue = xQueueCreate(10, sizeof(pet_event_t));
    if (!g_pet_event_queue) {
        ESP_LOGE(TAG, "创建事件队列失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建更新定时器
    esp_timer_create_args_t timer_args = {
        .callback = pet_update_timer_callback,
        .name = "pet_update"
    };
    esp_err_t ret = esp_timer_create(&timer_args, &g_pet_update_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建更新定时器失败");
        return ret;
    }

    // 初始化配置
    g_pet_config = DEFAULT_PET_CONFIG;

    // 初始化宠物数据
    memset(g_pets, 0, sizeof(g_pets));
    g_pet_count = 0;
    g_active_pet_id = 0;

    // 初始化子模块
    pet_ai_init();
    pet_emotion_init();
    pet_interaction_init();
    pet_lifecycle_init();

    g_pet_system_initialized = true;
    ESP_LOGI(TAG, "宠物系统初始化完成");

    return ESP_OK;
}

/**
 * @brief 启动宠物系统
 */
esp_err_t pet_system_start(void)
{
    if (!g_pet_system_initialized) {
        ESP_LOGE(TAG, "宠物系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_pet_system_running) {
        ESP_LOGW(TAG, "宠物系统已启动");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "启动宠物系统...");

    // 加载宠物数据
    pet_system_load_data();

    // 创建宠物管理任务
    BaseType_t ret = xTaskCreate(
        pet_system_task,
        "pet_system",
        4096,
        NULL,
        5,
        &g_pet_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建宠物管理任务失败");
        return ESP_ERR_NO_MEM;
    }

    // 启动更新定时器
    esp_err_t timer_ret = esp_timer_start_periodic(g_pet_update_timer, 
                                                  g_pet_config.update_interval_ms * 1000);
    if (timer_ret != ESP_OK) {
        ESP_LOGW(TAG, "启动更新定时器失败");
    }

    g_pet_system_running = true;
    ESP_LOGI(TAG, "宠物系统启动完成");

    return ESP_OK;
}

/**
 * @brief 停止宠物系统
 */
esp_err_t pet_system_stop(void)
{
    if (!g_pet_system_running) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止宠物系统...");

    g_pet_system_running = false;

    // 停止定时器
    if (g_pet_update_timer) {
        esp_timer_stop(g_pet_update_timer);
    }

    // 保存宠物数据
    pet_system_save_data();

    ESP_LOGI(TAG, "宠物系统停止完成");
    return ESP_OK;
}

/**
 * @brief 反初始化宠物系统
 */
esp_err_t pet_system_deinit(void)
{
    if (g_pet_system_running) {
        pet_system_stop();
    }

    if (!g_pet_system_initialized) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "反初始化宠物系统...");

    // 删除定时器
    if (g_pet_update_timer) {
        esp_timer_delete(g_pet_update_timer);
        g_pet_update_timer = NULL;
    }

    // 删除队列和互斥锁
    if (g_pet_event_queue) {
        vQueueDelete(g_pet_event_queue);
        g_pet_event_queue = NULL;
    }

    if (g_pet_mutex) {
        vSemaphoreDelete(g_pet_mutex);
        g_pet_mutex = NULL;
    }

    g_pet_system_initialized = false;
    ESP_LOGI(TAG, "宠物系统反初始化完成");

    return ESP_OK;
}

/**
 * @brief 设置宠物系统配置
 */
esp_err_t pet_system_set_config(const pet_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);
    g_pet_config = *config;
    xSemaphoreGive(g_pet_mutex);

    ESP_LOGI(TAG, "宠物系统配置已更新");
    return ESP_OK;
}

/**
 * @brief 获取宠物系统配置
 */
esp_err_t pet_system_get_config(pet_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);
    *config = g_pet_config;
    xSemaphoreGive(g_pet_mutex);

    return ESP_OK;
}

/**
 * @brief 注册事件回调
 */
esp_err_t pet_system_register_event_callback(pet_event_callback_t callback)
{
    g_event_callback = callback;
    return ESP_OK;
}

/**
 * @brief 创建新宠物
 */
esp_err_t pet_system_create_pet(pet_type_t type, const char *name, uint32_t *pet_id)
{
    if (!name || !pet_id) {
        return ESP_ERR_INVALID_ARG;
    }

    if (type >= PET_TYPE_MAX) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    if (g_pet_count >= g_pet_config.max_pets) {
        xSemaphoreGive(g_pet_mutex);
        ESP_LOGE(TAG, "宠物数量已达上限");
        return ESP_ERR_NO_MEM;
    }

    // 创建新宠物
    pet_info_t *pet = &g_pets[g_pet_count];
    memset(pet, 0, sizeof(pet_info_t));

    pet->id = g_next_pet_id++;
    strncpy(pet->name, name, sizeof(pet->name) - 1);
    pet->type = type;
    pet->stage = PET_STAGE_EGG;
    pet->state = PET_STATE_SLEEPING;
    pet->emotion = PET_EMOTION_NEUTRAL;
    pet->birth_time = time(NULL);
    pet->last_interaction = pet->birth_time;
    pet->is_alive = true;

    // 初始化属性
    pet->attributes.health = 100;
    pet->attributes.happiness = 50;
    pet->attributes.energy = 100;
    pet->attributes.hunger = 0;
    pet->attributes.cleanliness = 100;
    pet->attributes.intelligence = 10;
    pet->attributes.affection = 0;
    pet->attributes.experience = 0;
    pet->attributes.level = 1;

    g_pet_count++;
    *pet_id = pet->id;

    // 如果是第一只宠物，设为活跃宠物
    if (g_active_pet_id == 0) {
        g_active_pet_id = pet->id;
    }

    xSemaphoreGive(g_pet_mutex);

    // 保存到NVS
    save_pet_to_nvs(pet);

    // 发送出生事件
    send_pet_event(PET_EVENT_BORN, pet->id, NULL);

    ESP_LOGI(TAG, "创建宠物成功: ID=%d, 名称=%s, 类型=%d", pet->id, pet->name, pet->type);
    return ESP_OK;
}

/**
 * @brief 获取宠物信息
 */
esp_err_t pet_system_get_pet_info(uint32_t pet_id, pet_info_t *info)
{
    if (!info) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    for (uint8_t i = 0; i < g_pet_count; i++) {
        if (g_pets[i].id == pet_id) {
            *info = g_pets[i];
            xSemaphoreGive(g_pet_mutex);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_pet_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 获取当前活跃宠物ID
 */
uint32_t pet_system_get_active_pet_id(void)
{
    return g_active_pet_id;
}

/**
 * @brief 设置活跃宠物
 */
esp_err_t pet_system_set_active_pet(uint32_t pet_id)
{
    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    // 检查宠物是否存在
    bool found = false;
    for (uint8_t i = 0; i < g_pet_count; i++) {
        if (g_pets[i].id == pet_id && g_pets[i].is_alive) {
            found = true;
            break;
        }
    }

    if (found) {
        g_active_pet_id = pet_id;
        xSemaphoreGive(g_pet_mutex);
        ESP_LOGI(TAG, "设置活跃宠物: ID=%d", pet_id);
        return ESP_OK;
    }

    xSemaphoreGive(g_pet_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 喂食宠物
 */
esp_err_t pet_system_feed_pet(uint32_t pet_id)
{
    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    for (uint8_t i = 0; i < g_pet_count; i++) {
        if (g_pets[i].id == pet_id && g_pets[i].is_alive) {
            pet_info_t *pet = &g_pets[i];

            // 减少饥饿值
            if (pet->attributes.hunger > 20) {
                pet->attributes.hunger -= 20;
            } else {
                pet->attributes.hunger = 0;
            }

            // 增加快乐值
            if (pet->attributes.happiness < 90) {
                pet->attributes.happiness += 10;
            } else {
                pet->attributes.happiness = 100;
            }

            // 增加经验值
            pet->attributes.experience += 5;

            pet->last_feed = time(NULL);
            pet->last_interaction = pet->last_feed;
            pet->total_interactions++;

            xSemaphoreGive(g_pet_mutex);

            // 保存数据
            save_pet_to_nvs(pet);

            // 发送事件
            send_pet_event(PET_EVENT_FEED, pet_id, NULL);

            ESP_LOGI(TAG, "喂食宠物: ID=%d", pet_id);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_pet_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 与宠物玩耍
 */
esp_err_t pet_system_play_with_pet(uint32_t pet_id)
{
    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    for (uint8_t i = 0; i < g_pet_count; i++) {
        if (g_pets[i].id == pet_id && g_pets[i].is_alive) {
            pet_info_t *pet = &g_pets[i];

            // 增加快乐值
            if (pet->attributes.happiness < 85) {
                pet->attributes.happiness += 15;
            } else {
                pet->attributes.happiness = 100;
            }

            // 增加亲密度
            if (pet->attributes.affection < 95) {
                pet->attributes.affection += 5;
            } else {
                pet->attributes.affection = 100;
            }

            // 消耗精力
            if (pet->attributes.energy > 10) {
                pet->attributes.energy -= 10;
            }

            // 增加经验值
            pet->attributes.experience += 8;

            pet->last_interaction = time(NULL);
            pet->total_interactions++;
            pet->state = PET_STATE_PLAYING;

            xSemaphoreGive(g_pet_mutex);

            // 保存数据
            save_pet_to_nvs(pet);

            // 发送事件
            send_pet_event(PET_EVENT_PLAY, pet_id, NULL);

            ESP_LOGI(TAG, "与宠物玩耍: ID=%d", pet_id);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_pet_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 宠物系统任务
 */
static void pet_system_task(void *pvParameters)
{
    ESP_LOGI(TAG, "宠物系统任务启动");

    pet_event_t event;

    while (g_pet_system_running) {
        // 处理事件队列
        if (xQueueReceive(g_pet_event_queue, &event, pdMS_TO_TICKS(1000)) == pdTRUE) {
            // 调用事件回调
            if (g_event_callback) {
                g_event_callback(&event);
            }

            ESP_LOGD(TAG, "处理宠物事件: 类型=%d, 宠物ID=%d", event.type, event.pet_id);
        }

        // 定期检查宠物状态
        static uint32_t last_check_time = 0;
        uint32_t current_time = esp_timer_get_time() / 1000;
        if (current_time - last_check_time > 60000) { // 1分钟检查一次
            last_check_time = current_time;

            xSemaphoreTake(g_pet_mutex, portMAX_DELAY);
            for (uint8_t i = 0; i < g_pet_count; i++) {
                if (g_pets[i].is_alive) {
                    check_pet_needs(&g_pets[i]);
                }
            }
            xSemaphoreGive(g_pet_mutex);
        }
    }

    ESP_LOGI(TAG, "宠物系统任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 更新定时器回调
 */
static void pet_update_timer_callback(void *arg)
{
    if (!g_pet_system_running) {
        return;
    }

    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    for (uint8_t i = 0; i < g_pet_count; i++) {
        if (g_pets[i].is_alive) {
            update_pet_attributes(&g_pets[i]);
        }
    }

    xSemaphoreGive(g_pet_mutex);
}

/**
 * @brief 更新宠物属性
 */
static void update_pet_attributes(pet_info_t *pet)
{
    time_t now = time(NULL);
    time_t time_diff = now - pet->last_interaction;

    // 随时间自然变化
    if (time_diff > 3600) { // 1小时
        // 饥饿值增加
        if (pet->attributes.hunger < 95) {
            pet->attributes.hunger += 5;
        } else {
            pet->attributes.hunger = 100;
        }

        // 精力恢复
        if (pet->attributes.energy < 95) {
            pet->attributes.energy += 5;
        } else {
            pet->attributes.energy = 100;
        }

        // 清洁度下降
        if (pet->attributes.cleanliness > 5) {
            pet->attributes.cleanliness -= 5;
        } else {
            pet->attributes.cleanliness = 0;
        }
    }

    // 检查升级
    uint32_t exp_needed = pet->attributes.level * 100;
    if (pet->attributes.experience >= exp_needed) {
        pet->attributes.level++;
        pet->attributes.experience -= exp_needed;
        send_pet_event(PET_EVENT_LEVEL_UP, pet->id, &pet->attributes.level);
    }

    // 检查成长阶段
    pet_lifecycle_update(pet);
}

/**
 * @brief 检查宠物需求
 */
static void check_pet_needs(pet_info_t *pet)
{
    // 检查健康状态
    if (pet->attributes.hunger > 80 || pet->attributes.cleanliness < 20) {
        if (pet->attributes.health > 5) {
            pet->attributes.health -= 5;
        }

        if (pet->attributes.health < 30) {
            pet->state = PET_STATE_SICK;
            send_pet_event(PET_EVENT_SICK, pet->id, NULL);
        }
    }

    // 检查情绪变化
    pet_emotion_t old_emotion = pet->emotion;
    pet->emotion = pet_emotion_calculate(pet);

    if (old_emotion != pet->emotion) {
        send_pet_event(PET_EVENT_EMOTION_CHANGE, pet->id, &pet->emotion);
    }
}

/**
 * @brief 发送宠物事件
 */
static void send_pet_event(pet_event_type_t type, uint32_t pet_id, const void *data)
{
    pet_event_t event = {
        .type = type,
        .pet_id = pet_id,
        .timestamp = time(NULL)
    };

    if (data) {
        memcpy(&event.data, data, sizeof(event.data));
    }

    xQueueSend(g_pet_event_queue, &event, 0);
}

/**
 * @brief 保存宠物数据到NVS
 */
static esp_err_t save_pet_to_nvs(const pet_info_t *pet)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("pet_data", NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        return ret;
    }

    char key[16];
    snprintf(key, sizeof(key), "pet_%d", pet->id);

    ret = nvs_set_blob(nvs_handle, key, pet, sizeof(pet_info_t));
    if (ret == ESP_OK) {
        ret = nvs_commit(nvs_handle);
    }

    nvs_close(nvs_handle);
    return ret;
}

/**
 * @brief 从NVS加载宠物数据
 */
static esp_err_t load_pet_from_nvs(uint32_t pet_id, pet_info_t *pet)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("pet_data", NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        return ret;
    }

    char key[16];
    snprintf(key, sizeof(key), "pet_%d", pet_id);

    size_t required_size = sizeof(pet_info_t);
    ret = nvs_get_blob(nvs_handle, key, pet, &required_size);

    nvs_close(nvs_handle);
    return ret;
}

/**
 * @brief 保存宠物数据
 */
esp_err_t pet_system_save_data(void)
{
    ESP_LOGI(TAG, "保存宠物数据...");

    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    for (uint8_t i = 0; i < g_pet_count; i++) {
        save_pet_to_nvs(&g_pets[i]);
    }

    // 保存系统状态
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("pet_system", NVS_READWRITE, &nvs_handle);
    if (ret == ESP_OK) {
        nvs_set_u8(nvs_handle, "pet_count", g_pet_count);
        nvs_set_u32(nvs_handle, "active_pet", g_active_pet_id);
        nvs_set_u32(nvs_handle, "next_id", g_next_pet_id);
        nvs_commit(nvs_handle);
        nvs_close(nvs_handle);
    }

    xSemaphoreGive(g_pet_mutex);

    ESP_LOGI(TAG, "宠物数据保存完成");
    return ESP_OK;
}

/**
 * @brief 加载宠物数据
 */
esp_err_t pet_system_load_data(void)
{
    ESP_LOGI(TAG, "加载宠物数据...");

    // 加载系统状态
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("pet_system", NVS_READONLY, &nvs_handle);
    if (ret == ESP_OK) {
        size_t required_size;

        required_size = sizeof(uint8_t);
        nvs_get_u8(nvs_handle, "pet_count", &g_pet_count);

        required_size = sizeof(uint32_t);
        nvs_get_u32(nvs_handle, "active_pet", &g_active_pet_id);
        nvs_get_u32(nvs_handle, "next_id", &g_next_pet_id);

        nvs_close(nvs_handle);
    }

    // 加载宠物数据
    xSemaphoreTake(g_pet_mutex, portMAX_DELAY);

    for (uint8_t i = 0; i < g_pet_count; i++) {
        // 这里需要知道宠物ID，实际实现中可能需要遍历所有可能的ID
        // 简化处理，假设ID是连续的
        load_pet_from_nvs(i + 1, &g_pets[i]);
    }

    xSemaphoreGive(g_pet_mutex);

    ESP_LOGI(TAG, "宠物数据加载完成，宠物数量: %d", g_pet_count);
    return ESP_OK;
}
