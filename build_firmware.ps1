# TIMO项目固件编译脚本
# 用于编译主体固件和底座固件

param(
    [switch]$CleanBuild = $false,
    [switch]$MainOnly = $false,
    [switch]$BaseOnly = $false
)

Write-Host "=========================================="
Write-Host "TIMO项目固件编译脚本"
Write-Host "=========================================="

# 检查ESP-IDF环境
function Check-ESPIDFEnvironment {
    Write-Host "检查ESP-IDF环境..."
    
    # 检查IDF_PATH环境变量
    if (-not $env:IDF_PATH) {
        Write-Host "错误: ESP-IDF环境未设置" -ForegroundColor Red
        Write-Host "请先安装ESP-IDF并运行export.ps1脚本" -ForegroundColor Yellow
        Write-Host "或者运行: . `$env:IDF_PATH\export.ps1" -ForegroundColor Yellow
        return $false
    }
    
    Write-Host "ESP-IDF路径: $env:IDF_PATH" -ForegroundColor Green
    
    # 检查idf.py是否可用
    try {
        $idfVersion = & idf.py --version 2>&1
        Write-Host "ESP-IDF版本: $idfVersion" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "错误: idf.py命令不可用" -ForegroundColor Red
        Write-Host "请确保ESP-IDF环境正确配置" -ForegroundColor Yellow
        return $false
    }
}

# 编译项目
function Build-Project {
    param(
        [string]$ProjectPath,
        [string]$ProjectName,
        [string]$Target = "esp32s3"
    )
    
    Write-Host ""
    Write-Host "=========================================="
    Write-Host "编译 $ProjectName"
    Write-Host "=========================================="
    
    # 进入项目目录
    $originalPath = Get-Location
    Set-Location $ProjectPath
    
    try {
        # 清理构建（如果需要）
        if ($CleanBuild) {
            Write-Host "清理之前的构建..." -ForegroundColor Yellow
            & idf.py fullclean
        }
        
        # 设置目标芯片
        Write-Host "设置目标芯片为 $Target..." -ForegroundColor Cyan
        & idf.py set-target $Target
        
        if ($LASTEXITCODE -ne 0) {
            throw "设置目标芯片失败"
        }
        
        # 编译项目
        Write-Host "开始编译 $ProjectName..." -ForegroundColor Cyan
        & idf.py build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $ProjectName 编译成功!" -ForegroundColor Green
            
            # 显示固件信息
            Write-Host ""
            Write-Host "固件文件:" -ForegroundColor Yellow
            if (Test-Path "build") {
                Get-ChildItem -Path "build" -Filter "*.bin" | ForEach-Object {
                    $size = [math]::Round($_.Length / 1KB, 2)
                    Write-Host "  $($_.Name) - ${size} KB" -ForegroundColor White
                }
                
                # 显示主要固件文件
                $mainBin = "build\$ProjectName.bin"
                if (Test-Path $mainBin) {
                    $size = [math]::Round((Get-Item $mainBin).Length / 1KB, 2)
                    Write-Host ""
                    Write-Host "主固件文件: $mainBin (${size} KB)" -ForegroundColor Green
                }
            }
            
            return $true
        }
        else {
            Write-Host "✗ $ProjectName 编译失败!" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ 编译过程中发生错误: $_" -ForegroundColor Red
        return $false
    }
    finally {
        Set-Location $originalPath
    }
}

# 主程序
function Main {
    # 检查ESP-IDF环境
    if (-not (Check-ESPIDFEnvironment)) {
        Write-Host ""
        Write-Host "ESP-IDF环境检查失败，无法继续编译" -ForegroundColor Red
        Write-Host ""
        Write-Host "解决方案:" -ForegroundColor Yellow
        Write-Host "1. 安装ESP-IDF (https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/)" -ForegroundColor White
        Write-Host "2. 运行ESP-IDF环境设置脚本:" -ForegroundColor White
        Write-Host "   . `$env:IDF_PATH\export.ps1" -ForegroundColor Cyan
        Write-Host "3. 或者使用ESP-IDF命令提示符" -ForegroundColor White
        exit 1
    }
    
    $success = $true
    
    # 编译主体固件
    if (-not $BaseOnly) {
        Write-Host ""
        Write-Host "准备编译主体固件..." -ForegroundColor Magenta
        $mainSuccess = Build-Project -ProjectPath "1_main_device_firmware" -ProjectName "timo_main_device" -Target "esp32s3"
        $success = $success -and $mainSuccess
    }
    
    # 编译底座固件
    if (-not $MainOnly) {
        Write-Host ""
        Write-Host "准备编译底座固件..." -ForegroundColor Magenta
        $baseSuccess = Build-Project -ProjectPath "2_base_station_firmware" -ProjectName "timo_base_station" -Target "esp32c2"
        $success = $success -and $baseSuccess
    }
    
    # 总结
    Write-Host ""
    Write-Host "=========================================="
    if ($success) {
        Write-Host "✓ 所有固件编译完成!" -ForegroundColor Green
        Write-Host ""
        Write-Host "固件位置:" -ForegroundColor Yellow
        if (-not $BaseOnly) {
            Write-Host "  主体固件: 1_main_device_firmware\build\timo_main_device.bin" -ForegroundColor White
        }
        if (-not $MainOnly) {
            Write-Host "  底座固件: 2_base_station_firmware\build\timo_base_station.bin" -ForegroundColor White
        }
        Write-Host ""
        Write-Host "烧录命令:" -ForegroundColor Yellow
        if (-not $BaseOnly) {
            Write-Host "  主体设备: cd 1_main_device_firmware && idf.py flash monitor" -ForegroundColor Cyan
        }
        if (-not $MainOnly) {
            Write-Host "  底座设备: cd 2_base_station_firmware && idf.py flash monitor" -ForegroundColor Cyan
        }
    }
    else {
        Write-Host "✗ 部分或全部固件编译失败!" -ForegroundColor Red
        Write-Host "请检查错误信息并修复问题" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "=========================================="
}

# 显示帮助信息
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "TIMO项目固件编译脚本"
    Write-Host ""
    Write-Host "用法:"
    Write-Host "  .\build_firmware.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -CleanBuild    清理后重新编译"
    Write-Host "  -MainOnly      只编译主体固件"
    Write-Host "  -BaseOnly      只编译底座固件"
    Write-Host "  -h, --help     显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\build_firmware.ps1                # 编译所有固件"
    Write-Host "  .\build_firmware.ps1 -CleanBuild    # 清理后编译所有固件"
    Write-Host "  .\build_firmware.ps1 -MainOnly      # 只编译主体固件"
    exit 0
}

# 运行主程序
Main
