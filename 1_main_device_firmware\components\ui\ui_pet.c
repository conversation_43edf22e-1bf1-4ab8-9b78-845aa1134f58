/**
 * @file ui_pet.c
 * @brief TIMO虚拟宠物UI界面实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "ui_pet.h"
#include "ui_manager.h"
#include "ui_utils.h"
#include "pet_system.h"
#include "esp_log.h"
#include "lvgl.h"

static const char *TAG = "UI_PET";

/* UI对象 */
static lv_obj_t *g_pet_page = NULL;
static lv_obj_t *g_pet_avatar = NULL;
static lv_obj_t *g_pet_name_label = NULL;
static lv_obj_t *g_pet_status_label = NULL;
static lv_obj_t *g_pet_health_bar = NULL;
static lv_obj_t *g_pet_happiness_bar = NULL;
static lv_obj_t *g_pet_energy_bar = NULL;
static lv_obj_t *g_pet_level_label = NULL;
static lv_obj_t *g_pet_exp_bar = NULL;
static lv_obj_t *g_feed_btn = NULL;
static lv_obj_t *g_play_btn = NULL;
static lv_obj_t *g_clean_btn = NULL;
static lv_obj_t *g_talk_btn = NULL;

/* 当前宠物信息 */
static pet_info_t g_current_pet;
static bool g_pet_info_valid = false;

/* 前向声明 */
static void pet_feed_event_cb(lv_event_t *e);
static void pet_play_event_cb(lv_event_t *e);
static void pet_clean_event_cb(lv_event_t *e);
static void pet_talk_event_cb(lv_event_t *e);
static void pet_avatar_event_cb(lv_event_t *e);
static void update_pet_display(void);
static void create_pet_buttons(lv_obj_t *parent);
static void create_pet_status_bars(lv_obj_t *parent);

/**
 * @brief 初始化宠物UI
 */
esp_err_t ui_pet_init(void)
{
    ESP_LOGI(TAG, "初始化宠物UI...");
    
    // 创建宠物页面
    g_pet_page = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(g_pet_page, lv_color_hex(0x000000), 0);
    
    // 创建宠物头像
    g_pet_avatar = lv_img_create(g_pet_page);
    lv_obj_set_size(g_pet_avatar, 120, 120);
    lv_obj_align(g_pet_avatar, LV_ALIGN_TOP_MID, 0, 20);
    lv_obj_add_event_cb(g_pet_avatar, pet_avatar_event_cb, LV_EVENT_CLICKED, NULL);
    lv_obj_add_flag(g_pet_avatar, LV_OBJ_FLAG_CLICKABLE);
    
    // 创建宠物名称标签
    g_pet_name_label = lv_label_create(g_pet_page);
    lv_obj_set_style_text_color(g_pet_name_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_set_style_text_font(g_pet_name_label, &lv_font_montserrat_16, 0);
    lv_obj_align_to(g_pet_name_label, g_pet_avatar, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    lv_label_set_text(g_pet_name_label, "我的宠物");
    
    // 创建宠物状态标签
    g_pet_status_label = lv_label_create(g_pet_page);
    lv_obj_set_style_text_color(g_pet_status_label, lv_color_hex(0x00FF00), 0);
    lv_obj_set_style_text_font(g_pet_status_label, &lv_font_montserrat_12, 0);
    lv_obj_align_to(g_pet_status_label, g_pet_name_label, LV_ALIGN_OUT_BOTTOM_MID, 0, 5);
    lv_label_set_text(g_pet_status_label, "开心");
    
    // 创建等级标签
    g_pet_level_label = lv_label_create(g_pet_page);
    lv_obj_set_style_text_color(g_pet_level_label, lv_color_hex(0xFFD700), 0);
    lv_obj_set_style_text_font(g_pet_level_label, &lv_font_montserrat_14, 0);
    lv_obj_align_to(g_pet_level_label, g_pet_status_label, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    lv_label_set_text(g_pet_level_label, "等级 1");
    
    // 创建状态条
    create_pet_status_bars(g_pet_page);
    
    // 创建操作按钮
    create_pet_buttons(g_pet_page);
    
    ESP_LOGI(TAG, "宠物UI初始化完成");
    return ESP_OK;
}

/**
 * @brief 反初始化宠物UI
 */
esp_err_t ui_pet_deinit(void)
{
    if (g_pet_page) {
        lv_obj_del(g_pet_page);
        g_pet_page = NULL;
    }
    
    ESP_LOGI(TAG, "宠物UI反初始化完成");
    return ESP_OK;
}

/**
 * @brief 显示宠物页面
 */
void ui_pet_show_page(void)
{
    if (g_pet_page) {
        lv_obj_clear_flag(g_pet_page, LV_OBJ_FLAG_HIDDEN);
        
        // 更新宠物信息
        uint32_t active_pet_id = pet_system_get_active_pet_id();
        if (active_pet_id > 0) {
            if (pet_system_get_pet_info(active_pet_id, &g_current_pet) == ESP_OK) {
                g_pet_info_valid = true;
                update_pet_display();
            }
        }
    }
}

/**
 * @brief 隐藏宠物页面
 */
void ui_pet_hide_page(void)
{
    if (g_pet_page) {
        lv_obj_add_flag(g_pet_page, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief 更新宠物信息显示
 */
void ui_pet_update_info(const pet_info_t *pet_info)
{
    if (!pet_info) {
        return;
    }
    
    g_current_pet = *pet_info;
    g_pet_info_valid = true;
    
    if (g_pet_page && !lv_obj_has_flag(g_pet_page, LV_OBJ_FLAG_HIDDEN)) {
        update_pet_display();
    }
}

/**
 * @brief 创建状态条
 */
static void create_pet_status_bars(lv_obj_t *parent)
{
    // 健康值条
    lv_obj_t *health_label = lv_label_create(parent);
    lv_obj_set_style_text_color(health_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_align_to(health_label, g_pet_level_label, LV_ALIGN_OUT_BOTTOM_LEFT, -50, 20);
    lv_label_set_text(health_label, "健康");
    
    g_pet_health_bar = lv_bar_create(parent);
    lv_obj_set_size(g_pet_health_bar, 100, 8);
    lv_obj_align_to(g_pet_health_bar, health_label, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_set_style_bg_color(g_pet_health_bar, lv_color_hex(0x333333), LV_PART_MAIN);
    lv_obj_set_style_bg_color(g_pet_health_bar, lv_color_hex(0xFF0000), LV_PART_INDICATOR);
    lv_bar_set_range(g_pet_health_bar, 0, 100);
    
    // 快乐值条
    lv_obj_t *happiness_label = lv_label_create(parent);
    lv_obj_set_style_text_color(happiness_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_align_to(happiness_label, health_label, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 15);
    lv_label_set_text(happiness_label, "快乐");
    
    g_pet_happiness_bar = lv_bar_create(parent);
    lv_obj_set_size(g_pet_happiness_bar, 100, 8);
    lv_obj_align_to(g_pet_happiness_bar, happiness_label, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_set_style_bg_color(g_pet_happiness_bar, lv_color_hex(0x333333), LV_PART_MAIN);
    lv_obj_set_style_bg_color(g_pet_happiness_bar, lv_color_hex(0xFFFF00), LV_PART_INDICATOR);
    lv_bar_set_range(g_pet_happiness_bar, 0, 100);
    
    // 精力值条
    lv_obj_t *energy_label = lv_label_create(parent);
    lv_obj_set_style_text_color(energy_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_align_to(energy_label, happiness_label, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 15);
    lv_label_set_text(energy_label, "精力");
    
    g_pet_energy_bar = lv_bar_create(parent);
    lv_obj_set_size(g_pet_energy_bar, 100, 8);
    lv_obj_align_to(g_pet_energy_bar, energy_label, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_set_style_bg_color(g_pet_energy_bar, lv_color_hex(0x333333), LV_PART_MAIN);
    lv_obj_set_style_bg_color(g_pet_energy_bar, lv_color_hex(0x00FF00), LV_PART_INDICATOR);
    lv_bar_set_range(g_pet_energy_bar, 0, 100);
    
    // 经验值条
    lv_obj_t *exp_label = lv_label_create(parent);
    lv_obj_set_style_text_color(exp_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_align_to(exp_label, energy_label, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 15);
    lv_label_set_text(exp_label, "经验");
    
    g_pet_exp_bar = lv_bar_create(parent);
    lv_obj_set_size(g_pet_exp_bar, 100, 8);
    lv_obj_align_to(g_pet_exp_bar, exp_label, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_set_style_bg_color(g_pet_exp_bar, lv_color_hex(0x333333), LV_PART_MAIN);
    lv_obj_set_style_bg_color(g_pet_exp_bar, lv_color_hex(0x00FFFF), LV_PART_INDICATOR);
    lv_bar_set_range(g_pet_exp_bar, 0, 100);
}

/**
 * @brief 创建操作按钮
 */
static void create_pet_buttons(lv_obj_t *parent)
{
    // 喂食按钮
    g_feed_btn = lv_btn_create(parent);
    lv_obj_set_size(g_feed_btn, 80, 40);
    lv_obj_align(g_feed_btn, LV_ALIGN_BOTTOM_LEFT, 20, -20);
    lv_obj_add_event_cb(g_feed_btn, pet_feed_event_cb, LV_EVENT_CLICKED, NULL);
    
    lv_obj_t *feed_label = lv_label_create(g_feed_btn);
    lv_label_set_text(feed_label, "喂食");
    lv_obj_center(feed_label);
    
    // 玩耍按钮
    g_play_btn = lv_btn_create(parent);
    lv_obj_set_size(g_play_btn, 80, 40);
    lv_obj_align_to(g_play_btn, g_feed_btn, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_add_event_cb(g_play_btn, pet_play_event_cb, LV_EVENT_CLICKED, NULL);
    
    lv_obj_t *play_label = lv_label_create(g_play_btn);
    lv_label_set_text(play_label, "玩耍");
    lv_obj_center(play_label);
    
    // 清洁按钮
    g_clean_btn = lv_btn_create(parent);
    lv_obj_set_size(g_clean_btn, 80, 40);
    lv_obj_align_to(g_clean_btn, g_play_btn, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_add_event_cb(g_clean_btn, pet_clean_event_cb, LV_EVENT_CLICKED, NULL);
    
    lv_obj_t *clean_label = lv_label_create(g_clean_btn);
    lv_label_set_text(clean_label, "清洁");
    lv_obj_center(clean_label);
    
    // 对话按钮
    g_talk_btn = lv_btn_create(parent);
    lv_obj_set_size(g_talk_btn, 80, 40);
    lv_obj_align_to(g_talk_btn, g_clean_btn, LV_ALIGN_OUT_RIGHT_MID, 10, 0);
    lv_obj_add_event_cb(g_talk_btn, pet_talk_event_cb, LV_EVENT_CLICKED, NULL);
    
    lv_obj_t *talk_label = lv_label_create(g_talk_btn);
    lv_label_set_text(talk_label, "对话");
    lv_obj_center(talk_label);
}

/**
 * @brief 更新宠物显示
 */
static void update_pet_display(void)
{
    if (!g_pet_info_valid) {
        return;
    }
    
    // 更新名称
    if (g_pet_name_label) {
        lv_label_set_text(g_pet_name_label, g_current_pet.name);
    }
    
    // 更新状态
    if (g_pet_status_label) {
        const char *status_text = "未知";
        switch (g_current_pet.emotion) {
            case PET_EMOTION_HAPPY: status_text = "开心"; break;
            case PET_EMOTION_EXCITED: status_text = "兴奋"; break;
            case PET_EMOTION_CALM: status_text = "平静"; break;
            case PET_EMOTION_TIRED: status_text = "疲惫"; break;
            case PET_EMOTION_ANGRY: status_text = "愤怒"; break;
            case PET_EMOTION_SAD: status_text = "悲伤"; break;
            case PET_EMOTION_LONELY: status_text = "孤独"; break;
            case PET_EMOTION_CURIOUS: status_text = "好奇"; break;
            case PET_EMOTION_PLAYFUL: status_text = "顽皮"; break;
            default: status_text = "普通"; break;
        }
        lv_label_set_text(g_pet_status_label, status_text);
    }
    
    // 更新等级
    if (g_pet_level_label) {
        char level_text[32];
        snprintf(level_text, sizeof(level_text), "等级 %d", g_current_pet.attributes.level);
        lv_label_set_text(g_pet_level_label, level_text);
    }
    
    // 更新状态条
    if (g_pet_health_bar) {
        lv_bar_set_value(g_pet_health_bar, g_current_pet.attributes.health, LV_ANIM_ON);
    }
    
    if (g_pet_happiness_bar) {
        lv_bar_set_value(g_pet_happiness_bar, g_current_pet.attributes.happiness, LV_ANIM_ON);
    }
    
    if (g_pet_energy_bar) {
        lv_bar_set_value(g_pet_energy_bar, g_current_pet.attributes.energy, LV_ANIM_ON);
    }
    
    if (g_pet_exp_bar) {
        uint32_t exp_needed = g_current_pet.attributes.level * 100;
        uint8_t exp_percent = (g_current_pet.attributes.experience * 100) / exp_needed;
        lv_bar_set_value(g_pet_exp_bar, exp_percent, LV_ANIM_ON);
    }
}

/**
 * @brief 喂食按钮事件回调
 */
static void pet_feed_event_cb(lv_event_t *e)
{
    uint32_t active_pet_id = pet_system_get_active_pet_id();
    if (active_pet_id > 0) {
        esp_err_t ret = pet_system_feed_pet(active_pet_id);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "喂食宠物成功");
            // 更新显示
            if (pet_system_get_pet_info(active_pet_id, &g_current_pet) == ESP_OK) {
                update_pet_display();
            }
        } else {
            ESP_LOGE(TAG, "喂食宠物失败");
        }
    }
}

/**
 * @brief 玩耍按钮事件回调
 */
static void pet_play_event_cb(lv_event_t *e)
{
    uint32_t active_pet_id = pet_system_get_active_pet_id();
    if (active_pet_id > 0) {
        esp_err_t ret = pet_system_play_with_pet(active_pet_id);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "与宠物玩耍成功");
            // 更新显示
            if (pet_system_get_pet_info(active_pet_id, &g_current_pet) == ESP_OK) {
                update_pet_display();
            }
        } else {
            ESP_LOGE(TAG, "与宠物玩耍失败");
        }
    }
}

/**
 * @brief 清洁按钮事件回调
 */
static void pet_clean_event_cb(lv_event_t *e)
{
    uint32_t active_pet_id = pet_system_get_active_pet_id();
    if (active_pet_id > 0) {
        esp_err_t ret = pet_system_clean_pet(active_pet_id);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "清洁宠物成功");
            // 更新显示
            if (pet_system_get_pet_info(active_pet_id, &g_current_pet) == ESP_OK) {
                update_pet_display();
            }
        } else {
            ESP_LOGE(TAG, "清洁宠物失败");
        }
    }
}

/**
 * @brief 对话按钮事件回调
 */
static void pet_talk_event_cb(lv_event_t *e)
{
    uint32_t active_pet_id = pet_system_get_active_pet_id();
    if (active_pet_id > 0) {
        esp_err_t ret = pet_system_talk_to_pet(active_pet_id, "你好");
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "与宠物对话成功");
        } else {
            ESP_LOGE(TAG, "与宠物对话失败");
        }
    }
}

/**
 * @brief 宠物头像点击事件回调
 */
static void pet_avatar_event_cb(lv_event_t *e)
{
    uint32_t active_pet_id = pet_system_get_active_pet_id();
    if (active_pet_id > 0) {
        // 获取触摸坐标
        lv_point_t point;
        lv_indev_get_point(lv_indev_get_act(), &point);
        
        esp_err_t ret = pet_system_pet_touch(active_pet_id, point.x, point.y);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "抚摸宠物成功");
            // 更新显示
            if (pet_system_get_pet_info(active_pet_id, &g_current_pet) == ESP_OK) {
                update_pet_display();
            }
        }
    }
}
