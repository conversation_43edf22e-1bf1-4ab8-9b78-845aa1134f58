/**
 * @file ui_pet.h
 * @brief TIMO虚拟宠物UI界面头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef UI_PET_H
#define UI_PET_H

#include "esp_err.h"
#include "pet_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化宠物UI
 */
esp_err_t ui_pet_init(void);

/**
 * @brief 反初始化宠物UI
 */
esp_err_t ui_pet_deinit(void);

/**
 * @brief 显示宠物页面
 */
void ui_pet_show_page(void);

/**
 * @brief 隐藏宠物页面
 */
void ui_pet_hide_page(void);

/**
 * @brief 更新宠物信息显示
 */
void ui_pet_update_info(const pet_info_t *pet_info);

#ifdef __cplusplus
}
#endif

#endif // UI_PET_H
