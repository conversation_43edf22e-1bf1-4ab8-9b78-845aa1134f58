# This file was generated using idf.py save-defconfig. It can be edited manually.
# Espressif IoT Development Framework (ESP-IDF) 5.3.1 Project Minimal Configuration
#
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions_esp32.csv"
CONFIG_SR_MN_CN_MULTINET2_SINGLE_RECOGNITION=y
CONFIG_CN_SPEECH_COMMAND_ID8="jie neng mo shi"
CONFIG_CN_SPEECH_COMMAND_ID9="guan bi jie neng mo shi"
CONFIG_CN_SPEECH_COMMAND_ID11="guan bi chu shi mo shi"
CONFIG_CN_SPEECH_COMMAND_ID13="guan bi shui mian mo shi"
CONFIG_CN_SPEECH_COMMAND_ID14="ding shi yi xiao shi"
CONFIG_CN_SPEECH_COMMAND_ID15="ding shi liang xiao shi"
CONFIG_CN_SPEECH_COMMAND_ID16="zui da feng su,zui gao feng su"
CONFIG_CN_SPEECH_COMMAND_ID17=""
CONFIG_CN_SPEECH_COMMAND_ID18=""
CONFIG_CN_SPEECH_COMMAND_ID19=""
CONFIG_SPIRAM=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
