/**
 * @file notification_sender.c
 * @brief TIMO通知发送器实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "alert_system.h"
#include "esp_log.h"

static const char *TAG = "NOTIFICATION_SENDER";

/**
 * @brief 通知发送器初始化
 */
esp_err_t notification_sender_init(void)
{
    ESP_LOGI(TAG, "初始化通知发送器...");
    // TODO: 实现通知发送器初始化
    ESP_LOGI(TAG, "通知发送器初始化完成");
    return ESP_OK;
}

/**
 * @brief 通知发送器反初始化
 */
esp_err_t notification_sender_deinit(void)
{
    ESP_LOGI(TAG, "反初始化通知发送器...");
    // TODO: 实现通知发送器反初始化
    ESP_LOGI(TAG, "通知发送器反初始化完成");
    return ESP_OK;
}
