/**
 * 设备数据模型
 */

const mongoose = require('mongoose');

const deviceSchema = new mongoose.Schema({
  // 设备基本信息
  deviceId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  deviceType: {
    type: String,
    enum: ['main', 'base'],
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  description: {
    type: String,
    trim: true,
    maxlength: 200
  },
  
  // 所有者信息
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 硬件信息
  hardware: {
    chipModel: {
      type: String,
      required: true
    },
    macAddress: {
      type: String,
      required: true,
      unique: true
    },
    serialNumber: {
      type: String,
      unique: true
    },
    firmwareVersion: {
      type: String,
      required: true
    },
    hardwareVersion: {
      type: String
    }
  },
  
  // 网络信息
  network: {
    ipAddress: {
      type: String
    },
    wifiSSID: {
      type: String
    },
    signalStrength: {
      type: Number
    },
    lastSeen: {
      type: Date,
      default: Date.now
    }
  },
  
  // 设备状态
  status: {
    type: String,
    enum: ['online', 'offline', 'maintenance', 'error'],
    default: 'offline'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  
  // 配置信息
  config: {
    timezone: {
      type: String,
      default: 'Asia/Shanghai'
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    volume: {
      type: Number,
      default: 70,
      min: 0,
      max: 100
    },
    brightness: {
      type: Number,
      default: 80,
      min: 0,
      max: 100
    },
    autoSleep: {
      enabled: {
        type: Boolean,
        default: true
      },
      startTime: {
        type: String,
        default: '22:00'
      },
      endTime: {
        type: String,
        default: '07:00'
      }
    },
    voiceSettings: {
      engine: {
        type: String,
        enum: ['local', 'esp-sr', 'cloud', 'hybrid'],
        default: 'hybrid'
      },
      wakeWord: {
        type: String,
        default: 'Hi,乐鑫'
      },
      sensitivity: {
        type: Number,
        default: 0.5,
        min: 0,
        max: 1
      }
    }
  },
  
  // 传感器配置
  sensors: {
    temperature: {
      enabled: {
        type: Boolean,
        default: true
      },
      interval: {
        type: Number,
        default: 60000
      },
      thresholds: {
        min: {
          type: Number,
          default: 18
        },
        max: {
          type: Number,
          default: 28
        }
      }
    },
    humidity: {
      enabled: {
        type: Boolean,
        default: true
      },
      interval: {
        type: Number,
        default: 60000
      },
      thresholds: {
        min: {
          type: Number,
          default: 30
        },
        max: {
          type: Number,
          default: 70
        }
      }
    },
    co2: {
      enabled: {
        type: Boolean,
        default: true
      },
      interval: {
        type: Number,
        default: 300000
      },
      threshold: {
        type: Number,
        default: 1000
      }
    },
    light: {
      enabled: {
        type: Boolean,
        default: true
      },
      interval: {
        type: Number,
        default: 60000
      },
      autoBrightness: {
        type: Boolean,
        default: true
      }
    }
  },
  
  // 统计信息
  stats: {
    totalUptime: {
      type: Number,
      default: 0
    },
    lastReboot: {
      type: Date
    },
    errorCount: {
      type: Number,
      default: 0
    },
    dataPoints: {
      type: Number,
      default: 0
    }
  },
  
  // 位置信息
  location: {
    room: {
      type: String,
      trim: true
    },
    coordinates: {
      latitude: {
        type: Number
      },
      longitude: {
        type: Number
      }
    }
  },
  
  // 配对信息（主体设备与底座设备）
  pairedDevice: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device'
  },
  
  // 注册和认证
  registrationDate: {
    type: Date,
    default: Date.now
  },
  authToken: {
    type: String
  },
  tokenExpiry: {
    type: Date
  }
}, {
  timestamps: true
});

// 索引
deviceSchema.index({ deviceId: 1 });
deviceSchema.index({ owner: 1 });
deviceSchema.index({ 'hardware.macAddress': 1 });
deviceSchema.index({ status: 1 });
deviceSchema.index({ deviceType: 1 });
deviceSchema.index({ 'network.lastSeen': -1 });

// 虚拟字段
deviceSchema.virtual('isOnline').get(function() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.network.lastSeen > fiveMinutesAgo;
});

deviceSchema.virtual('uptimeHours').get(function() {
  return Math.round(this.stats.totalUptime / (1000 * 60 * 60));
});

// 实例方法
deviceSchema.methods.updateLastSeen = function() {
  this.network.lastSeen = new Date();
  return this.save();
};

deviceSchema.methods.updateStatus = function(status) {
  this.status = status;
  return this.save();
};

deviceSchema.methods.generateAuthToken = function() {
  const crypto = require('crypto');
  this.authToken = crypto.randomBytes(32).toString('hex');
  this.tokenExpiry = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30天
  return this.save();
};

deviceSchema.methods.isTokenValid = function() {
  return this.tokenExpiry && this.tokenExpiry > new Date();
};

// 静态方法
deviceSchema.statics.findByDeviceId = function(deviceId) {
  return this.findOne({ deviceId });
};

deviceSchema.statics.findByOwner = function(ownerId) {
  return this.find({ owner: ownerId, isActive: true });
};

deviceSchema.statics.findOnlineDevices = function() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.find({
    'network.lastSeen': { $gte: fiveMinutesAgo },
    isActive: true
  });
};

deviceSchema.statics.findByType = function(deviceType) {
  return this.find({ deviceType, isActive: true });
};

// 中间件
deviceSchema.pre('save', function(next) {
  if (this.isNew) {
    this.registrationDate = new Date();
  }
  next();
});

module.exports = mongoose.model('Device', deviceSchema);
