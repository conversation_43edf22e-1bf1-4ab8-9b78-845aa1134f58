/**
 * @file app_main.c
 * @brief TIMO智能闹钟应用程序主逻辑
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "app_main.h"
#include "hardware_hal.h"
#include "system_manager.h"
#include "ui_manager.h"
#include "time_manager.h"
#include "alarm_manager.h"
#include "sensor_system.h"
#include "audio_system.h"
#include "pet_system.h"
#include "task_system.h"
#include "alert_system.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_system.h"
#include "esp_sleep.h"

static const char *TAG = "APP_MAIN";

/* 全局事件组和队列 */
EventGroupHandle_t g_system_event_group = NULL;
QueueHandle_t g_sensor_data_queue = NULL;
QueueHandle_t g_audio_event_queue = NULL;
QueueHandle_t g_ui_event_queue = NULL;

/* 任务句柄 */
static TaskHandle_t h_hardware_init_task = NULL;
static TaskHandle_t h_system_monitor_task = NULL;

/**
 * @brief 硬件初始化任务
 */
static void hardware_init_task(void *pvParameters)
{
    ESP_LOGI(TAG, "开始硬件初始化...");
    
    // 初始化系统管理器
    ESP_LOGI(TAG, "初始化系统管理器...");
    esp_err_t ret = system_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "系统管理器初始化失败");
        return;
    }

    // 启动系统管理器
    ret = system_manager_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "系统管理器启动失败");
        return;
    }

    // 初始化所有硬件组件
    ESP_LOGI(TAG, "开始硬件初始化...");
    ret = hardware_init_all();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "硬件初始化成功");
        xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_SENSORS_READY);
        xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_AUDIO_READY);
        xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_UI_READY);
    } else {
        ESP_LOGE(TAG, "硬件初始化失败，系统将以降级模式运行");
    }

    // 打印硬件状态
    hardware_print_status();

    // 初始化RTC时钟
    ESP_LOGI(TAG, "初始化RTC时钟...");
    ret = rtc_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "RTC时钟初始化成功");
    } else {
        ESP_LOGW(TAG, "RTC时钟初始化失败");
    }

    xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_BLUETOOTH_READY);

    // 初始化UI管理器
    ESP_LOGI(TAG, "初始化UI管理器...");
    ret = ui_manager_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "UI管理器初始化成功");
        ret = ui_manager_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "UI管理器启动成功");
            xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_UI_READY);
        } else {
            ESP_LOGE(TAG, "UI管理器启动失败");
        }
    } else {
        ESP_LOGE(TAG, "UI管理器初始化失败");
    }

    // 初始化时间管理器
    ESP_LOGI(TAG, "初始化时间管理器...");
    ret = time_manager_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "时间管理器初始化成功");
        ret = time_manager_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "时间管理器启动成功");
        } else {
            ESP_LOGE(TAG, "时间管理器启动失败");
        }
    } else {
        ESP_LOGE(TAG, "时间管理器初始化失败");
    }

    // 初始化闹钟管理器
    ESP_LOGI(TAG, "初始化闹钟管理器...");
    ret = alarm_manager_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "闹钟管理器初始化成功");
        ret = alarm_manager_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "闹钟管理器启动成功");
        } else {
            ESP_LOGE(TAG, "闹钟管理器启动失败");
        }
    } else {
        ESP_LOGE(TAG, "闹钟管理器初始化失败");
    }

    // 初始化传感器系统
    ESP_LOGI(TAG, "初始化传感器系统...");
    ret = sensor_system_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "传感器系统初始化成功");
        ret = sensor_system_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "传感器系统启动成功");
        } else {
            ESP_LOGE(TAG, "传感器系统启动失败");
        }
    } else {
        ESP_LOGE(TAG, "传感器系统初始化失败");
    }

    // 初始化音频系统
    ESP_LOGI(TAG, "初始化音频系统...");
    ret = audio_system_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "音频系统初始化成功");
        ret = audio_system_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "音频系统启动成功");
            xEventGroupSetBits(g_system_event_group, SYSTEM_EVENT_AUDIO_READY);
        } else {
            ESP_LOGE(TAG, "音频系统启动失败");
        }
    } else {
        ESP_LOGE(TAG, "音频系统初始化失败");
    }

    // 初始化虚拟宠物系统
    ESP_LOGI(TAG, "初始化虚拟宠物系统...");
    ret = pet_system_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "虚拟宠物系统初始化成功");
        ret = pet_system_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "虚拟宠物系统启动成功");
        } else {
            ESP_LOGE(TAG, "虚拟宠物系统启动失败");
        }
    } else {
        ESP_LOGE(TAG, "虚拟宠物系统初始化失败");
    }

    // 初始化任务管理系统
    ESP_LOGI(TAG, "初始化任务管理系统...");
    ret = task_system_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "任务管理系统初始化成功");
        ret = task_system_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "任务管理系统启动成功");
        } else {
            ESP_LOGE(TAG, "任务管理系统启动失败");
        }
    } else {
        ESP_LOGE(TAG, "任务管理系统初始化失败");
    }

    // 初始化预警系统
    ESP_LOGI(TAG, "初始化预警系统...");
    ret = alert_system_init();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "预警系统初始化成功");
        ret = alert_system_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "预警系统启动成功");
        } else {
            ESP_LOGE(TAG, "预警系统启动失败");
        }
    } else {
        ESP_LOGE(TAG, "预警系统初始化失败");
    }

    ESP_LOGI(TAG, "硬件初始化完成");

    // 删除任务
    vTaskDelete(NULL);
}

/**
 * @brief 系统监控任务
 */
static void system_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "系统监控任务启动");
    
    while (1) {
        // 等待所有系统组件就绪
        EventBits_t bits = xEventGroupWaitBits(
            g_system_event_group,
            SYSTEM_EVENT_SENSORS_READY | SYSTEM_EVENT_AUDIO_READY | 
            SYSTEM_EVENT_BLUETOOTH_READY | SYSTEM_EVENT_UI_READY,
            pdFALSE,
            pdTRUE,
            pdMS_TO_TICKS(1000)
        );
        
        if ((bits & (SYSTEM_EVENT_SENSORS_READY | SYSTEM_EVENT_AUDIO_READY | 
                    SYSTEM_EVENT_BLUETOOTH_READY | SYSTEM_EVENT_UI_READY)) == 
            (SYSTEM_EVENT_SENSORS_READY | SYSTEM_EVENT_AUDIO_READY | 
             SYSTEM_EVENT_BLUETOOTH_READY | SYSTEM_EVENT_UI_READY)) {
            ESP_LOGI(TAG, "所有系统组件已就绪");
            break;
        }
        
        ESP_LOGI(TAG, "等待系统组件就绪... (0x%08x)", bits);
    }
    
    // 系统监控循环
    while (1) {
        // 监控内存使用情况
        size_t free_heap = esp_get_free_heap_size();
        size_t min_free_heap = esp_get_minimum_free_heap_size();
        
        ESP_LOGI(TAG, "内存状态 - 当前空闲: %d bytes, 最小空闲: %d bytes", 
                 free_heap, min_free_heap);
        
        // 监控任务状态
        UBaseType_t task_count = uxTaskGetNumberOfTasks();
        ESP_LOGI(TAG, "当前任务数量: %d", task_count);
        
        // 延时10秒
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}

/**
 * @brief 应用程序主启动函数
 */
esp_err_t app_main_start(void)
{
    ESP_LOGI(TAG, "启动TIMO智能闹钟应用程序");
    
    // 创建全局事件组
    g_system_event_group = xEventGroupCreate();
    if (g_system_event_group == NULL) {
        ESP_LOGE(TAG, "创建系统事件组失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建全局队列
    g_sensor_data_queue = xQueueCreate(10, sizeof(void*));
    g_audio_event_queue = xQueueCreate(5, sizeof(void*));
    g_ui_event_queue = xQueueCreate(10, sizeof(void*));
    
    if (!g_sensor_data_queue || !g_audio_event_queue || !g_ui_event_queue) {
        ESP_LOGE(TAG, "创建全局队列失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建硬件初始化任务
    BaseType_t ret = xTaskCreate(
        hardware_init_task,
        "hardware_init",
        TASK_STACK_SIZE_LARGE,
        NULL,
        TASK_PRIORITY_HIGH,
        &h_hardware_init_task
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建硬件初始化任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    // 创建系统监控任务
    ret = xTaskCreate(
        system_monitor_task,
        "system_monitor",
        TASK_STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_LOW,
        &h_system_monitor_task
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建系统监控任务失败");
        return ESP_ERR_NO_MEM;
    }
    
    ESP_LOGI(TAG, "应用程序启动完成");
    return ESP_OK;
}

/**
 * @brief 获取系统运行时间（毫秒）
 */
uint64_t app_get_uptime_ms(void)
{
    return esp_timer_get_time() / 1000;
}

/**
 * @brief 系统重启
 */
void app_system_restart(void)
{
    ESP_LOGI(TAG, "系统重启...");
    esp_restart();
}

/**
 * @brief 进入深度睡眠模式
 */
void app_enter_deep_sleep(uint64_t sleep_time_us)
{
    ESP_LOGI(TAG, "进入深度睡眠模式，睡眠时间: %llu us", sleep_time_us);
    esp_sleep_enable_timer_wakeup(sleep_time_us);
    esp_deep_sleep_start();
}
