/**
 * 任务管理路由
 */

const express = require('express');
const Joi = require('joi');
const Task = require('../models/Task');
const User = require('../models/User');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const logger = require('../utils/logger');

const router = express.Router();

// 验证模式
const createTaskSchema = Joi.object({
  title: Joi.string().max(100).required(),
  description: Joi.string().max(500).optional(),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').optional(),
  type: Joi.string().valid('general', 'work', 'study', 'life', 'health', 'entertainment').optional(),
  dueDate: Joi.date().optional(),
  reminderTime: Joi.date().optional(),
  estimatedMinutes: Joi.number().min(0).optional(),
  tags: Joi.array().items(Joi.string().max(20)).optional(),
  recurring: Joi.object({
    enabled: Joi.boolean().optional(),
    pattern: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly', 'custom').optional(),
    interval: Joi.number().min(1).optional(),
    daysOfWeek: Joi.array().items(Joi.number().min(0).max(6)).optional(),
    endDate: Joi.date().optional(),
    maxOccurrences: Joi.number().min(1).optional()
  }).optional(),
  reminder: Joi.object({
    enabled: Joi.boolean().optional(),
    methods: Joi.array().items(Joi.string().valid('sound', 'vibration', 'light', 'notification')).optional(),
    advanceMinutes: Joi.number().min(0).optional(),
    repeat: Joi.object({
      enabled: Joi.boolean().optional(),
      intervalMinutes: Joi.number().min(1).optional(),
      maxRepeats: Joi.number().min(1).optional()
    }).optional()
  }).optional(),
  pomodoro: Joi.object({
    enabled: Joi.boolean().optional(),
    workDuration: Joi.number().min(1).optional(),
    shortBreak: Joi.number().min(1).optional(),
    longBreak: Joi.number().min(1).optional()
  }).optional()
});

const updateTaskSchema = Joi.object({
  title: Joi.string().max(100).optional(),
  description: Joi.string().max(500).optional(),
  status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').optional(),
  priority: Joi.string().valid('low', 'normal', 'high', 'urgent').optional(),
  type: Joi.string().valid('general', 'work', 'study', 'life', 'health', 'entertainment').optional(),
  dueDate: Joi.date().optional(),
  reminderTime: Joi.date().optional(),
  progress: Joi.number().min(0).max(100).optional(),
  estimatedMinutes: Joi.number().min(0).optional(),
  actualMinutes: Joi.number().min(0).optional(),
  tags: Joi.array().items(Joi.string().max(20)).optional(),
  recurring: Joi.object({
    enabled: Joi.boolean().optional(),
    pattern: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly', 'custom').optional(),
    interval: Joi.number().min(1).optional(),
    daysOfWeek: Joi.array().items(Joi.number().min(0).max(6)).optional(),
    endDate: Joi.date().optional(),
    maxOccurrences: Joi.number().min(1).optional()
  }).optional(),
  reminder: Joi.object({
    enabled: Joi.boolean().optional(),
    methods: Joi.array().items(Joi.string().valid('sound', 'vibration', 'light', 'notification')).optional(),
    advanceMinutes: Joi.number().min(0).optional(),
    repeat: Joi.object({
      enabled: Joi.boolean().optional(),
      intervalMinutes: Joi.number().min(1).optional(),
      maxRepeats: Joi.number().min(1).optional()
    }).optional()
  }).optional(),
  pomodoro: Joi.object({
    enabled: Joi.boolean().optional(),
    workDuration: Joi.number().min(1).optional(),
    shortBreak: Joi.number().min(1).optional(),
    longBreak: Joi.number().min(1).optional(),
    completedSessions: Joi.number().min(0).optional(),
    totalWorkTime: Joi.number().min(0).optional()
  }).optional()
});

// 创建任务
router.post('/', auth, validate(createTaskSchema), async (req, res) => {
  try {
    const taskData = {
      ...req.body,
      owner: req.userId
    };
    
    const task = new Task(taskData);
    await task.save();
    
    // 更新用户任务统计
    await User.findByIdAndUpdate(req.userId, {
      $inc: { 'stats.tasksCount': 1 }
    });
    
    logger.info(`Task created: ${task._id} by user ${req.userId}`);
    
    res.status(201).json({
      message: 'Task created successfully',
      task: {
        id: task._id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        type: task.type,
        dueDate: task.dueDate,
        reminderTime: task.reminderTime,
        progress: task.progress,
        tags: task.tags,
        createdAt: task.createdAt
      }
    });
  } catch (error) {
    logger.error('Create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: error.message
    });
  }
});

// 获取任务列表
router.get('/', auth, async (req, res) => {
  try {
    const {
      status,
      priority,
      type,
      tag,
      dueDate,
      page = 1,
      limit = 20,
      sort = '-createdAt'
    } = req.query;
    
    const filter = { owner: req.userId };
    
    if (status) {
      filter.status = status;
    }
    
    if (priority) {
      filter.priority = priority;
    }
    
    if (type) {
      filter.type = type;
    }
    
    if (tag) {
      filter.tags = tag;
    }
    
    if (dueDate) {
      const date = new Date(dueDate);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
      filter.dueDate = {
        $gte: startOfDay,
        $lt: endOfDay
      };
    }
    
    const skip = (page - 1) * limit;
    
    const [tasks, total] = await Promise.all([
      Task.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Task.countDocuments(filter)
    ]);
    
    res.json({
      tasks: tasks.map(task => ({
        id: task._id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        type: task.type,
        dueDate: task.dueDate,
        reminderTime: task.reminderTime,
        progress: task.progress,
        estimatedMinutes: task.estimatedMinutes,
        actualMinutes: task.actualMinutes,
        tags: task.tags,
        subtasks: task.subtasks,
        isOverdue: task.dueDate && task.dueDate < new Date() && task.status !== 'completed',
        completionRate: task.subtasks.length > 0 ? 
          Math.round((task.subtasks.filter(st => st.completed).length / task.subtasks.length) * 100) : 
          (task.status === 'completed' ? 100 : 0),
        createdAt: task.createdAt,
        updatedAt: task.updatedAt
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    logger.error('Get tasks error:', error);
    res.status(500).json({
      error: 'Failed to get tasks',
      message: error.message
    });
  }
});

// 获取今日任务
router.get('/today', auth, async (req, res) => {
  try {
    const tasks = await Task.findDueToday(req.userId);
    
    res.json({
      tasks: tasks.map(task => ({
        id: task._id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        type: task.type,
        dueDate: task.dueDate,
        progress: task.progress,
        tags: task.tags,
        isOverdue: task.isOverdue,
        completionRate: task.completionRate
      }))
    });
  } catch (error) {
    logger.error('Get today tasks error:', error);
    res.status(500).json({
      error: 'Failed to get today tasks',
      message: error.message
    });
  }
});

// 获取过期任务
router.get('/overdue', auth, async (req, res) => {
  try {
    const tasks = await Task.findOverdue(req.userId);
    
    res.json({
      tasks: tasks.map(task => ({
        id: task._id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        dueDate: task.dueDate,
        progress: task.progress,
        tags: task.tags,
        overdueDays: Math.floor((Date.now() - task.dueDate) / (1000 * 60 * 60 * 24))
      }))
    });
  } catch (error) {
    logger.error('Get overdue tasks error:', error);
    res.status(500).json({
      error: 'Failed to get overdue tasks',
      message: error.message
    });
  }
});

// 获取单个任务
router.get('/:taskId', auth, async (req, res) => {
  try {
    const task = await Task.findOne({
      _id: req.params.taskId,
      owner: req.userId
    });
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found'
      });
    }
    
    // 增加查看次数
    task.stats.viewCount += 1;
    await task.save();
    
    res.json({
      task: {
        id: task._id,
        title: task.title,
        description: task.description,
        status: task.status,
        priority: task.priority,
        type: task.type,
        dueDate: task.dueDate,
        reminderTime: task.reminderTime,
        completedAt: task.completedAt,
        progress: task.progress,
        estimatedMinutes: task.estimatedMinutes,
        actualMinutes: task.actualMinutes,
        tags: task.tags,
        subtasks: task.subtasks,
        attachments: task.attachments,
        recurring: task.recurring,
        reminder: task.reminder,
        pomodoro: task.pomodoro,
        stats: task.stats,
        isOverdue: task.isOverdue,
        completionRate: task.completionRate,
        efficiency: task.efficiency,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt
      }
    });
  } catch (error) {
    logger.error('Get task error:', error);
    res.status(500).json({
      error: 'Failed to get task',
      message: error.message
    });
  }
});

// 更新任务
router.put('/:taskId', auth, validate(updateTaskSchema), async (req, res) => {
  try {
    const task = await Task.findOne({
      _id: req.params.taskId,
      owner: req.userId
    });
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found'
      });
    }
    
    // 更新任务字段
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        if (typeof req.body[key] === 'object' && !Array.isArray(req.body[key]) && req.body[key] !== null) {
          task[key] = { ...task[key], ...req.body[key] };
        } else {
          task[key] = req.body[key];
        }
      }
    });
    
    // 如果状态变为完成，更新完成时间和用户统计
    if (req.body.status === 'completed' && task.status !== 'completed') {
      task.completedAt = new Date();
      task.progress = 100;
      
      await User.findByIdAndUpdate(req.userId, {
        $inc: { 'stats.completedTasks': 1 }
      });
    }
    
    await task.save();
    
    logger.info(`Task updated: ${req.params.taskId} by user ${req.userId}`);
    
    res.json({
      message: 'Task updated successfully',
      task: {
        id: task._id,
        title: task.title,
        status: task.status,
        progress: task.progress,
        updatedAt: task.updatedAt
      }
    });
  } catch (error) {
    logger.error('Update task error:', error);
    res.status(500).json({
      error: 'Failed to update task',
      message: error.message
    });
  }
});

// 完成任务
router.post('/:taskId/complete', auth, async (req, res) => {
  try {
    const task = await Task.findOne({
      _id: req.params.taskId,
      owner: req.userId
    });
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found'
      });
    }
    
    if (task.status === 'completed') {
      return res.status(400).json({
        error: 'Task already completed'
      });
    }
    
    await task.markCompleted();
    
    // 更新用户统计
    await User.findByIdAndUpdate(req.userId, {
      $inc: { 'stats.completedTasks': 1 }
    });
    
    logger.info(`Task completed: ${req.params.taskId} by user ${req.userId}`);
    
    res.json({
      message: 'Task completed successfully',
      task: {
        id: task._id,
        title: task.title,
        status: task.status,
        completedAt: task.completedAt,
        progress: task.progress
      }
    });
  } catch (error) {
    logger.error('Complete task error:', error);
    res.status(500).json({
      error: 'Failed to complete task',
      message: error.message
    });
  }
});

// 添加子任务
router.post('/:taskId/subtasks', auth, async (req, res) => {
  try {
    const { title } = req.body;
    
    if (!title) {
      return res.status(400).json({
        error: 'Subtask title is required'
      });
    }
    
    const task = await Task.findOne({
      _id: req.params.taskId,
      owner: req.userId
    });
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found'
      });
    }
    
    await task.addSubtask(title);
    
    res.json({
      message: 'Subtask added successfully',
      subtasks: task.subtasks
    });
  } catch (error) {
    logger.error('Add subtask error:', error);
    res.status(500).json({
      error: 'Failed to add subtask',
      message: error.message
    });
  }
});

// 完成子任务
router.post('/:taskId/subtasks/:subtaskId/complete', auth, async (req, res) => {
  try {
    const task = await Task.findOne({
      _id: req.params.taskId,
      owner: req.userId
    });
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found'
      });
    }
    
    await task.completeSubtask(req.params.subtaskId);
    
    res.json({
      message: 'Subtask completed successfully',
      progress: task.progress,
      subtasks: task.subtasks
    });
  } catch (error) {
    logger.error('Complete subtask error:', error);
    res.status(500).json({
      error: 'Failed to complete subtask',
      message: error.message
    });
  }
});

// 删除任务
router.delete('/:taskId', auth, async (req, res) => {
  try {
    const task = await Task.findOneAndDelete({
      _id: req.params.taskId,
      owner: req.userId
    });
    
    if (!task) {
      return res.status(404).json({
        error: 'Task not found'
      });
    }
    
    // 更新用户任务统计
    await User.findByIdAndUpdate(req.userId, {
      $inc: { 'stats.tasksCount': -1 }
    });
    
    logger.info(`Task deleted: ${req.params.taskId} by user ${req.userId}`);
    
    res.json({
      message: 'Task deleted successfully'
    });
  } catch (error) {
    logger.error('Delete task error:', error);
    res.status(500).json({
      error: 'Failed to delete task',
      message: error.message
    });
  }
});

// 获取任务统计
router.get('/stats/summary', auth, async (req, res) => {
  try {
    const [
      totalTasks,
      pendingTasks,
      completedTasks,
      overdueTasks,
      todayTasks
    ] = await Promise.all([
      Task.countDocuments({ owner: req.userId }),
      Task.countDocuments({ owner: req.userId, status: 'pending' }),
      Task.countDocuments({ owner: req.userId, status: 'completed' }),
      Task.countDocuments({
        owner: req.userId,
        dueDate: { $lt: new Date() },
        status: { $ne: 'completed' }
      }),
      Task.findDueToday(req.userId).countDocuments()
    ]);
    
    const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
    
    res.json({
      stats: {
        totalTasks,
        pendingTasks,
        completedTasks,
        overdueTasks,
        todayTasks,
        completionRate
      }
    });
  } catch (error) {
    logger.error('Get task stats error:', error);
    res.status(500).json({
      error: 'Failed to get task statistics',
      message: error.message
    });
  }
});

module.exports = router;
