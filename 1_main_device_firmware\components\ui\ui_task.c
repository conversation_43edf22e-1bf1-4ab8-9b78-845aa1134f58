/**
 * @file ui_task.c
 * @brief TIMO任务管理UI界面实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "ui_task.h"
#include "ui_manager.h"
#include "esp_log.h"
#include "lvgl.h"

static const char *TAG = "UI_TASK";

/* UI对象 */
static lv_obj_t *g_task_page = NULL;

/**
 * @brief 初始化任务UI
 */
esp_err_t ui_task_init(void)
{
    ESP_LOGI(TAG, "初始化任务UI...");
    
    // 创建任务页面
    g_task_page = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(g_task_page, lv_color_hex(0x000000), 0);
    
    // 创建标题
    lv_obj_t *title_label = lv_label_create(g_task_page);
    lv_obj_set_style_text_color(title_label, lv_color_hex(0xFFFFFF), 0);
    lv_obj_set_style_text_font(title_label, &lv_font_montserrat_18, 0);
    lv_obj_align(title_label, LV_ALIGN_TOP_MID, 0, 20);
    lv_label_set_text(title_label, "任务管理");
    
    ESP_LOGI(TAG, "任务UI初始化完成");
    return ESP_OK;
}

/**
 * @brief 反初始化任务UI
 */
esp_err_t ui_task_deinit(void)
{
    if (g_task_page) {
        lv_obj_del(g_task_page);
        g_task_page = NULL;
    }
    
    ESP_LOGI(TAG, "任务UI反初始化完成");
    return ESP_OK;
}

/**
 * @brief 显示任务页面
 */
void ui_task_show_page(void)
{
    if (g_task_page) {
        lv_obj_clear_flag(g_task_page, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief 隐藏任务页面
 */
void ui_task_hide_page(void)
{
    if (g_task_page) {
        lv_obj_add_flag(g_task_page, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief 更新任务列表显示
 */
void ui_task_update_list(void)
{
    ESP_LOGI(TAG, "更新任务列表显示");
    // TODO: 实现任务列表更新
}

/**
 * @brief 显示任务创建对话框
 */
void ui_task_show_create_dialog(void)
{
    ESP_LOGI(TAG, "显示任务创建对话框");
    // TODO: 实现任务创建对话框
}

/**
 * @brief 显示任务详情
 */
void ui_task_show_details(uint32_t task_id)
{
    ESP_LOGI(TAG, "显示任务详情: ID=%d", task_id);
    // TODO: 实现任务详情显示
}
