/**
 * @file pet_lifecycle.h
 * @brief TIMO虚拟宠物生命周期管理头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef PET_LIFECYCLE_H
#define PET_LIFECYCLE_H

#include "esp_err.h"
#include "pet_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化宠物生命周期管理
 */
esp_err_t pet_lifecycle_init(void);

/**
 * @brief 反初始化宠物生命周期管理
 */
esp_err_t pet_lifecycle_deinit(void);

/**
 * @brief 更新宠物生命周期
 */
esp_err_t pet_lifecycle_update(pet_info_t *pet);

#ifdef __cplusplus
}
#endif

#endif // PET_LIFECYCLE_H
