/**
 * @file alert_system.c
 * @brief TIMO环境监测预警系统实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "alert_system.h"
#include "sensor_system.h"
#include "audio_system.h"
#include "bluetooth_system.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <string.h>
#include <time.h>

static const char *TAG = "ALERT_SYSTEM";

/* 系统状态 */
static bool g_alert_system_initialized = false;
static bool g_alert_system_running = false;
static bool g_alert_muted = false;
static time_t g_mute_end_time = 0;

/* 任务和同步 */
static TaskHandle_t g_alert_task_handle = NULL;
static SemaphoreHandle_t g_alert_mutex = NULL;
static QueueHandle_t g_alert_event_queue = NULL;
static esp_timer_handle_t g_alert_check_timer = NULL;

/* 预警数据 */
static alert_info_t g_alerts[16];  // 最多16个活跃预警
static uint32_t g_alert_count = 0;
static uint32_t g_next_alert_id = 1;

/* 配置和回调 */
static alert_system_config_t g_alert_config;
static alert_event_callback_t g_event_callback = NULL;

/* 默认配置 */
static const alert_system_config_t DEFAULT_ALERT_CONFIG = {
    .enabled = true,
    .check_interval_s = 30,  // 30秒检查一次
    .auto_resolve = true,
    .auto_resolve_delay_s = 300,  // 5分钟自动解决
    .sound_enabled = true,
    .light_enabled = true,
    .notification_enabled = true,
    .default_volume = 70,
    .thresholds = {
        .temp_min = 18.0,
        .temp_max = 28.0,
        .humidity_min = 30.0,
        .humidity_max = 70.0,
        .co2_max = 1000,
        .light_min = 50.0,
        .light_max = 2000.0,
        .aqi_max = 150,
        .comfort_min = 60
    }
};

/* 前向声明 */
static void alert_system_task(void *pvParameters);
static void alert_check_timer_callback(void *arg);
static void check_environment_alerts(void);
static esp_err_t trigger_alert_action(const alert_info_t *alert);
static esp_err_t save_alert_to_nvs(const alert_info_t *alert);
static void send_alert_event(alert_event_type_t type, uint32_t alert_id, const alert_info_t *alert);

/**
 * @brief 初始化预警系统
 */
esp_err_t alert_system_init(void)
{
    if (g_alert_system_initialized) {
        ESP_LOGW(TAG, "预警系统已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化预警系统...");

    // 创建互斥锁
    g_alert_mutex = xSemaphoreCreateMutex();
    if (!g_alert_mutex) {
        ESP_LOGE(TAG, "创建互斥锁失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建事件队列
    g_alert_event_queue = xQueueCreate(10, sizeof(alert_event_t));
    if (!g_alert_event_queue) {
        ESP_LOGE(TAG, "创建事件队列失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建检查定时器
    esp_timer_create_args_t timer_args = {
        .callback = alert_check_timer_callback,
        .name = "alert_check"
    };
    esp_err_t ret = esp_timer_create(&timer_args, &g_alert_check_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建检查定时器失败");
        return ret;
    }

    // 初始化配置
    g_alert_config = DEFAULT_ALERT_CONFIG;

    // 初始化预警数据
    memset(g_alerts, 0, sizeof(g_alerts));
    g_alert_count = 0;

    g_alert_system_initialized = true;
    ESP_LOGI(TAG, "预警系统初始化完成");

    return ESP_OK;
}

/**
 * @brief 启动预警系统
 */
esp_err_t alert_system_start(void)
{
    if (!g_alert_system_initialized) {
        ESP_LOGE(TAG, "预警系统未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_alert_system_running) {
        ESP_LOGW(TAG, "预警系统已启动");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "启动预警系统...");

    // 加载预警数据
    alert_system_load_data();

    // 创建预警管理任务
    BaseType_t ret = xTaskCreate(
        alert_system_task,
        "alert_system",
        4096,
        NULL,
        6,  // 高优先级
        &g_alert_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建预警管理任务失败");
        return ESP_ERR_NO_MEM;
    }

    // 启动检查定时器
    esp_err_t timer_ret = esp_timer_start_periodic(g_alert_check_timer, 
                                                  g_alert_config.check_interval_s * 1000000);
    if (timer_ret != ESP_OK) {
        ESP_LOGW(TAG, "启动检查定时器失败");
    }

    g_alert_system_running = true;
    ESP_LOGI(TAG, "预警系统启动完成");

    return ESP_OK;
}

/**
 * @brief 停止预警系统
 */
esp_err_t alert_system_stop(void)
{
    if (!g_alert_system_running) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止预警系统...");

    g_alert_system_running = false;

    // 停止定时器
    if (g_alert_check_timer) {
        esp_timer_stop(g_alert_check_timer);
    }

    // 保存预警数据
    alert_system_save_data();

    ESP_LOGI(TAG, "预警系统停止完成");
    return ESP_OK;
}

/**
 * @brief 手动触发预警
 */
esp_err_t alert_system_trigger_alert(alert_type_t type, alert_level_t level, 
                                    const char *message, float value)
{
    if (!g_alert_system_running || !g_alert_config.enabled) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "触发预警: 类型=%d, 级别=%d, 消息=%s, 值=%.2f", type, level, message, value);

    xSemaphoreTake(g_alert_mutex, portMAX_DELAY);

    if (g_alert_count >= sizeof(g_alerts) / sizeof(g_alerts[0])) {
        xSemaphoreGive(g_alert_mutex);
        ESP_LOGE(TAG, "预警数量已达上限");
        return ESP_ERR_NO_MEM;
    }

    // 创建新预警
    alert_info_t *alert = &g_alerts[g_alert_count];
    memset(alert, 0, sizeof(alert_info_t));

    alert->id = g_next_alert_id++;
    alert->type = type;
    alert->level = level;
    alert->timestamp = time(NULL);
    alert->is_active = true;
    alert->is_acknowledged = false;
    alert->trigger_value = value;
    
    if (message) {
        strncpy(alert->message, message, sizeof(alert->message) - 1);
    }

    g_alert_count++;

    xSemaphoreGive(g_alert_mutex);

    // 保存到NVS
    save_alert_to_nvs(alert);

    // 触发预警动作
    trigger_alert_action(alert);

    // 发送预警事件
    send_alert_event(ALERT_EVENT_TRIGGERED, alert->id, alert);

    ESP_LOGI(TAG, "预警触发成功: ID=%d", alert->id);
    return ESP_OK;
}

/**
 * @brief 确认预警
 */
esp_err_t alert_system_acknowledge_alert(uint32_t alert_id)
{
    xSemaphoreTake(g_alert_mutex, portMAX_DELAY);

    for (uint32_t i = 0; i < g_alert_count; i++) {
        if (g_alerts[i].id == alert_id && g_alerts[i].is_active) {
            g_alerts[i].is_acknowledged = true;
            
            xSemaphoreGive(g_alert_mutex);
            
            // 保存数据
            save_alert_to_nvs(&g_alerts[i]);
            
            // 发送确认事件
            send_alert_event(ALERT_EVENT_ACKNOWLEDGED, alert_id, &g_alerts[i]);
            
            ESP_LOGI(TAG, "确认预警: ID=%d", alert_id);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_alert_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 解决预警
 */
esp_err_t alert_system_resolve_alert(uint32_t alert_id)
{
    xSemaphoreTake(g_alert_mutex, portMAX_DELAY);

    for (uint32_t i = 0; i < g_alert_count; i++) {
        if (g_alerts[i].id == alert_id && g_alerts[i].is_active) {
            g_alerts[i].is_active = false;
            g_alerts[i].resolved_time = time(NULL);
            
            xSemaphoreGive(g_alert_mutex);
            
            // 保存数据
            save_alert_to_nvs(&g_alerts[i]);
            
            // 发送解决事件
            send_alert_event(ALERT_EVENT_RESOLVED, alert_id, &g_alerts[i]);
            
            ESP_LOGI(TAG, "解决预警: ID=%d", alert_id);
            return ESP_OK;
        }
    }

    xSemaphoreGive(g_alert_mutex);
    return ESP_ERR_NOT_FOUND;
}

/**
 * @brief 静音预警
 */
esp_err_t alert_system_mute(uint32_t duration_s)
{
    g_alert_muted = true;
    g_mute_end_time = time(NULL) + duration_s;
    
    ESP_LOGI(TAG, "预警系统静音 %d 秒", duration_s);
    return ESP_OK;
}

/**
 * @brief 取消静音
 */
esp_err_t alert_system_unmute(void)
{
    g_alert_muted = false;
    g_mute_end_time = 0;
    
    ESP_LOGI(TAG, "取消预警系统静音");
    return ESP_OK;
}

/**
 * @brief 检查是否静音
 */
bool alert_system_is_muted(void)
{
    if (g_alert_muted) {
        time_t now = time(NULL);
        if (g_mute_end_time > 0 && now >= g_mute_end_time) {
            g_alert_muted = false;
            g_mute_end_time = 0;
        }
    }
    
    return g_alert_muted;
}

/**
 * @brief 预警系统任务
 */
static void alert_system_task(void *pvParameters)
{
    ESP_LOGI(TAG, "预警系统任务启动");
    
    alert_event_t event;
    
    while (g_alert_system_running) {
        // 处理事件队列
        if (xQueueReceive(g_alert_event_queue, &event, pdMS_TO_TICKS(1000)) == pdTRUE) {
            // 调用事件回调
            if (g_event_callback) {
                g_event_callback(&event);
            }
            
            ESP_LOGD(TAG, "处理预警事件: 类型=%d, 预警ID=%d", event.event_type, event.alert_id);
        }
    }
    
    ESP_LOGI(TAG, "预警系统任务结束");
    vTaskDelete(NULL);
}

/**
 * @brief 检查定时器回调
 */
static void alert_check_timer_callback(void *arg)
{
    if (!g_alert_system_running || !g_alert_config.enabled) {
        return;
    }
    
    check_environment_alerts();
}

/**
 * @brief 检查环境预警
 */
static void check_environment_alerts(void)
{
    // 获取传感器数据
    environment_data_t env_data;
    if (sensor_system_get_latest_data(&env_data) != ESP_OK) {
        return;
    }
    
    // 检查温度
    if (env_data.temperature < g_alert_config.thresholds.temp_min || 
        env_data.temperature > g_alert_config.thresholds.temp_max) {
        alert_system_trigger_alert(ALERT_TYPE_TEMPERATURE, ALERT_LEVEL_MEDIUM, 
                                  "温度异常", env_data.temperature);
    }
    
    // 检查湿度
    if (env_data.humidity < g_alert_config.thresholds.humidity_min || 
        env_data.humidity > g_alert_config.thresholds.humidity_max) {
        alert_system_trigger_alert(ALERT_TYPE_HUMIDITY, ALERT_LEVEL_LOW, 
                                  "湿度异常", env_data.humidity);
    }
    
    // 检查CO2
    if (env_data.co2 > g_alert_config.thresholds.co2_max) {
        alert_level_t level = env_data.co2 > 1500 ? ALERT_LEVEL_HIGH : ALERT_LEVEL_MEDIUM;
        alert_system_trigger_alert(ALERT_TYPE_CO2, level, 
                                  "CO2浓度过高", env_data.co2);
    }
    
    // 检查舒适度
    uint8_t comfort_score = sensor_system_get_comfort_score();
    if (comfort_score < g_alert_config.thresholds.comfort_min) {
        alert_system_trigger_alert(ALERT_TYPE_COMFORT, ALERT_LEVEL_INFO, 
                                  "环境舒适度较低", comfort_score);
    }
}

/**
 * @brief 触发预警动作
 */
static esp_err_t trigger_alert_action(const alert_info_t *alert)
{
    if (!alert || alert_system_is_muted()) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "执行预警动作: 类型=%d, 级别=%d", alert->type, alert->level);
    
    // 声音预警
    if (g_alert_config.sound_enabled) {
        // TODO: 播放预警声音
        ESP_LOGD(TAG, "播放预警声音");
    }
    
    // 灯光预警
    if (g_alert_config.light_enabled) {
        // 根据预警级别设置灯光颜色
        uint32_t color = 0xFFFF00;  // 默认黄色
        switch (alert->level) {
            case ALERT_LEVEL_LOW:
                color = 0xFFFF00;  // 黄色
                break;
            case ALERT_LEVEL_MEDIUM:
                color = 0xFF8000;  // 橙色
                break;
            case ALERT_LEVEL_HIGH:
            case ALERT_LEVEL_CRITICAL:
                color = 0xFF0000;  // 红色
                break;
            default:
                color = 0x00FF00;  // 绿色
                break;
        }
        
        // TODO: 设置底座灯光
        ESP_LOGD(TAG, "设置预警灯光: 颜色=0x%06X", color);
    }
    
    return ESP_OK;
}

/**
 * @brief 发送预警事件
 */
static void send_alert_event(alert_event_type_t type, uint32_t alert_id, const alert_info_t *alert)
{
    alert_event_t event = {
        .event_type = type,
        .alert_id = alert_id,
        .timestamp = time(NULL)
    };
    
    if (alert) {
        event.alert_type = alert->type;
        event.alert_level = alert->level;
        event.value = alert->trigger_value;
    }
    
    xQueueSend(g_alert_event_queue, &event, 0);
}

/**
 * @brief 保存预警数据到NVS
 */
static esp_err_t save_alert_to_nvs(const alert_info_t *alert)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("alert_data", NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        return ret;
    }
    
    char key[16];
    snprintf(key, sizeof(key), "alert_%d", alert->id);
    
    ret = nvs_set_blob(nvs_handle, key, alert, sizeof(alert_info_t));
    if (ret == ESP_OK) {
        ret = nvs_commit(nvs_handle);
    }
    
    nvs_close(nvs_handle);
    return ret;
}

/**
 * @brief 保存预警数据
 */
esp_err_t alert_system_save_data(void)
{
    ESP_LOGI(TAG, "保存预警数据...");
    
    xSemaphoreTake(g_alert_mutex, portMAX_DELAY);
    
    for (uint32_t i = 0; i < g_alert_count; i++) {
        save_alert_to_nvs(&g_alerts[i]);
    }
    
    xSemaphoreGive(g_alert_mutex);
    
    ESP_LOGI(TAG, "预警数据保存完成");
    return ESP_OK;
}

/**
 * @brief 加载预警数据
 */
esp_err_t alert_system_load_data(void)
{
    ESP_LOGI(TAG, "加载预警数据...");
    
    // TODO: 实现从NVS加载预警数据
    
    ESP_LOGI(TAG, "预警数据加载完成");
    return ESP_OK;
}
