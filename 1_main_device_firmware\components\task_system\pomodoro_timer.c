/**
 * @file pomodoro_timer.c
 * @brief TIMO番茄时钟实现
 * @version 1.0.0
 * @date 2025-06-30
 */

#include "task_system.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include <time.h>

static const char *TAG = "POMODORO";

/* 番茄时钟状态 */
static pomodoro_info_t g_pomodoro_info = {0};
static pomodoro_config_t g_pomodoro_config;
static bool g_pomodoro_initialized = false;
static bool g_pomodoro_running = false;

/* 定时器和同步 */
static esp_timer_handle_t g_pomodoro_timer = NULL;
static SemaphoreHandle_t g_pomodoro_mutex = NULL;

/* 外部事件回调 */
extern task_event_callback_t g_event_callback;
extern void send_task_event(task_event_type_t type, uint32_t task_id, const void *data);

/* 前向声明 */
static void pomodoro_timer_callback(void *arg);
static void pomodoro_complete_cycle(void);
static void pomodoro_start_break(bool is_long_break);
static void pomodoro_start_work(void);

/**
 * @brief 初始化番茄时钟
 */
static esp_err_t pomodoro_init(void)
{
    if (g_pomodoro_initialized) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化番茄时钟...");

    // 创建互斥锁
    g_pomodoro_mutex = xSemaphoreCreateMutex();
    if (!g_pomodoro_mutex) {
        ESP_LOGE(TAG, "创建番茄时钟互斥锁失败");
        return ESP_ERR_NO_MEM;
    }

    // 创建定时器
    esp_timer_create_args_t timer_args = {
        .callback = pomodoro_timer_callback,
        .name = "pomodoro_timer"
    };
    esp_err_t ret = esp_timer_create(&timer_args, &g_pomodoro_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建番茄时钟定时器失败");
        return ret;
    }

    // 初始化配置
    g_pomodoro_config = (pomodoro_config_t){
        .work_duration_min = 25,
        .short_break_min = 5,
        .long_break_min = 15,
        .cycles_before_long_break = 4,
        .auto_start_breaks = false,
        .auto_start_work = false,
        .sound_enabled = true,
        .vibration_enabled = false,
        .focus_level = 5
    };

    // 初始化状态
    memset(&g_pomodoro_info, 0, sizeof(g_pomodoro_info));
    g_pomodoro_info.state = POMODORO_STATE_IDLE;

    g_pomodoro_initialized = true;
    ESP_LOGI(TAG, "番茄时钟初始化完成");

    return ESP_OK;
}

/**
 * @brief 开始番茄时钟
 */
esp_err_t pomodoro_start(uint32_t task_id)
{
    if (!g_pomodoro_initialized) {
        esp_err_t ret = pomodoro_init();
        if (ret != ESP_OK) {
            return ret;
        }
    }

    if (g_pomodoro_running) {
        ESP_LOGW(TAG, "番茄时钟已在运行");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "开始番茄时钟，任务ID: %d", task_id);

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);

    // 初始化番茄时钟信息
    g_pomodoro_info.current_task_id = task_id;
    g_pomodoro_info.state = POMODORO_STATE_WORK;
    g_pomodoro_info.total_seconds = g_pomodoro_config.work_duration_min * 60;
    g_pomodoro_info.remaining_seconds = g_pomodoro_info.total_seconds;
    g_pomodoro_info.current_cycle = 1;
    g_pomodoro_info.session_start_time = time(NULL);

    xSemaphoreGive(g_pomodoro_mutex);

    // 启动定时器 (每秒触发一次)
    esp_err_t ret = esp_timer_start_periodic(g_pomodoro_timer, 1000000);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "启动番茄时钟定时器失败");
        return ret;
    }

    g_pomodoro_running = true;

    // 发送开始事件
    send_task_event(TASK_EVENT_POMODORO_START, task_id, &g_pomodoro_info.state);

    ESP_LOGI(TAG, "番茄时钟启动成功");
    return ESP_OK;
}

/**
 * @brief 暂停番茄时钟
 */
esp_err_t pomodoro_pause(void)
{
    if (!g_pomodoro_running) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "暂停番茄时钟");

    // 停止定时器
    esp_timer_stop(g_pomodoro_timer);

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);
    g_pomodoro_info.state = POMODORO_STATE_PAUSED;
    xSemaphoreGive(g_pomodoro_mutex);

    // 发送暂停事件
    send_task_event(TASK_EVENT_POMODORO_PAUSE, g_pomodoro_info.current_task_id, &g_pomodoro_info.state);

    return ESP_OK;
}

/**
 * @brief 恢复番茄时钟
 */
esp_err_t pomodoro_resume(void)
{
    if (!g_pomodoro_running || g_pomodoro_info.state != POMODORO_STATE_PAUSED) {
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "恢复番茄时钟");

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);
    g_pomodoro_info.state = POMODORO_STATE_WORK;
    xSemaphoreGive(g_pomodoro_mutex);

    // 重新启动定时器
    esp_err_t ret = esp_timer_start_periodic(g_pomodoro_timer, 1000000);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "恢复番茄时钟定时器失败");
        return ret;
    }

    return ESP_OK;
}

/**
 * @brief 停止番茄时钟
 */
esp_err_t pomodoro_stop(void)
{
    if (!g_pomodoro_running) {
        return ESP_OK;
    }

    ESP_LOGI(TAG, "停止番茄时钟");

    // 停止定时器
    esp_timer_stop(g_pomodoro_timer);

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);
    
    uint32_t task_id = g_pomodoro_info.current_task_id;
    
    // 重置状态
    memset(&g_pomodoro_info, 0, sizeof(g_pomodoro_info));
    g_pomodoro_info.state = POMODORO_STATE_IDLE;
    
    xSemaphoreGive(g_pomodoro_mutex);

    g_pomodoro_running = false;

    ESP_LOGI(TAG, "番茄时钟停止完成");
    return ESP_OK;
}

/**
 * @brief 获取番茄时钟信息
 */
esp_err_t pomodoro_get_info(pomodoro_info_t *info)
{
    if (!info) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);
    *info = g_pomodoro_info;
    xSemaphoreGive(g_pomodoro_mutex);

    return ESP_OK;
}

/**
 * @brief 设置番茄时钟配置
 */
esp_err_t pomodoro_set_config(const pomodoro_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);
    g_pomodoro_config = *config;
    xSemaphoreGive(g_pomodoro_mutex);

    ESP_LOGI(TAG, "番茄时钟配置已更新");
    return ESP_OK;
}

/**
 * @brief 获取番茄时钟配置
 */
esp_err_t pomodoro_get_config(pomodoro_config_t *config)
{
    if (!config) {
        return ESP_ERR_INVALID_ARG;
    }

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);
    *config = g_pomodoro_config;
    xSemaphoreGive(g_pomodoro_mutex);

    return ESP_OK;
}

/**
 * @brief 番茄时钟定时器回调
 */
static void pomodoro_timer_callback(void *arg)
{
    if (!g_pomodoro_running) {
        return;
    }

    xSemaphoreTake(g_pomodoro_mutex, portMAX_DELAY);

    if (g_pomodoro_info.remaining_seconds > 0) {
        g_pomodoro_info.remaining_seconds--;
        
        // 更新统计
        if (g_pomodoro_info.state == POMODORO_STATE_WORK) {
            g_pomodoro_info.total_work_time++;
        } else if (g_pomodoro_info.state == POMODORO_STATE_SHORT_BREAK || 
                   g_pomodoro_info.state == POMODORO_STATE_LONG_BREAK) {
            g_pomodoro_info.total_break_time++;
        }
    } else {
        // 时间到了
        pomodoro_complete_cycle();
    }

    xSemaphoreGive(g_pomodoro_mutex);
}

/**
 * @brief 完成一个周期
 */
static void pomodoro_complete_cycle(void)
{
    ESP_LOGI(TAG, "番茄时钟周期完成，状态: %d", g_pomodoro_info.state);

    if (g_pomodoro_info.state == POMODORO_STATE_WORK) {
        // 工作周期完成
        g_pomodoro_info.completed_cycles++;
        
        // 发送完成事件
        send_task_event(TASK_EVENT_POMODORO_COMPLETE, g_pomodoro_info.current_task_id, 
                       &g_pomodoro_info.completed_cycles);

        // 判断是否需要长休息
        bool is_long_break = (g_pomodoro_info.current_cycle % g_pomodoro_config.cycles_before_long_break) == 0;
        
        if (g_pomodoro_config.auto_start_breaks) {
            pomodoro_start_break(is_long_break);
        } else {
            // 停止定时器，等待用户手动开始休息
            esp_timer_stop(g_pomodoro_timer);
            g_pomodoro_info.state = POMODORO_STATE_IDLE;
        }
        
    } else if (g_pomodoro_info.state == POMODORO_STATE_SHORT_BREAK || 
               g_pomodoro_info.state == POMODORO_STATE_LONG_BREAK) {
        // 休息周期完成
        send_task_event(TASK_EVENT_BREAK_END, g_pomodoro_info.current_task_id, NULL);
        
        if (g_pomodoro_config.auto_start_work) {
            pomodoro_start_work();
        } else {
            // 停止定时器，等待用户手动开始工作
            esp_timer_stop(g_pomodoro_timer);
            g_pomodoro_info.state = POMODORO_STATE_IDLE;
        }
    }
}

/**
 * @brief 开始休息
 */
static void pomodoro_start_break(bool is_long_break)
{
    ESP_LOGI(TAG, "开始%s休息", is_long_break ? "长" : "短");

    g_pomodoro_info.state = is_long_break ? POMODORO_STATE_LONG_BREAK : POMODORO_STATE_SHORT_BREAK;
    g_pomodoro_info.total_seconds = is_long_break ? 
        g_pomodoro_config.long_break_min * 60 : g_pomodoro_config.short_break_min * 60;
    g_pomodoro_info.remaining_seconds = g_pomodoro_info.total_seconds;

    // 发送休息开始事件
    send_task_event(TASK_EVENT_BREAK_START, g_pomodoro_info.current_task_id, &g_pomodoro_info.state);
}

/**
 * @brief 开始工作
 */
static void pomodoro_start_work(void)
{
    ESP_LOGI(TAG, "开始工作周期 %d", g_pomodoro_info.current_cycle + 1);

    g_pomodoro_info.state = POMODORO_STATE_WORK;
    g_pomodoro_info.current_cycle++;
    g_pomodoro_info.total_seconds = g_pomodoro_config.work_duration_min * 60;
    g_pomodoro_info.remaining_seconds = g_pomodoro_info.total_seconds;

    // 发送工作开始事件
    send_task_event(TASK_EVENT_POMODORO_START, g_pomodoro_info.current_task_id, &g_pomodoro_info.state);
}
