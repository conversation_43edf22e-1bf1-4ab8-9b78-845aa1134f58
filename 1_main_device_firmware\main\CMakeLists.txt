idf_component_register(
    SRCS
        "main.c"
        "app_main.c"
        "esp_sr_test.c"
    INCLUDE_DIRS 
        "."
        "include"
    REQUIRES
        hardware
        system
        ui
        time_manager
        sensor_system
        audio_system
        voice_system
        pet_system
        task_system
        alert_system
        bluetooth_system
        driver
        esp_wifi
        esp_netif
        esp_event
        nvs_flash
        esp_http_client
        esp_https_ota
        fatfs
        wear_levelling
        bt
        esp_bt
        esp_gatt
        esp_gap_ble
        esp_gattc
        esp_gatts
        esp_a2dp
        esp_avrc
        esp_hf_client
        esp_spp
        esp_timer
        esp_adc
        esp_pm
        esp_sleep
        esp_system
        esp_common
        freertos
        log
        spi_flash
        esp_partition
        esp_ota
        esp_app_format
        esp_bootloader_format
        esp_serial_slave_link
        esp_local_ctrl
        esp_https_server
        esp_http_server
        esp_websocket_client
        esp_eth
        esp_ringbuf
        esp_hw_support
        hal
        soc
        esp_rom
        esp_mm
        heap
        esp_psram
        esp_lcd
        esp_codec_dev
        esp_audio_front_end
        esp_audio_processor
        esp_audio_effects
)
