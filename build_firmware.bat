@echo off
REM TIMO项目固件编译脚本
REM 用于编译主体固件和底座固件

echo ==========================================
echo TIMO项目固件编译脚本
echo ==========================================

REM 检查ESP-IDF环境
if "%IDF_PATH%"=="" (
    echo 错误: ESP-IDF环境未设置
    echo 请先安装ESP-IDF并运行export.bat脚本
    echo 或者运行: %IDF_PATH%\export.bat
    pause
    exit /b 1
)

echo ESP-IDF路径: %IDF_PATH%

REM 检查idf.py是否可用
idf.py --version >nul 2>&1
if errorlevel 1 (
    echo 错误: idf.py命令不可用
    echo 请确保ESP-IDF环境正确配置
    pause
    exit /b 1
)

echo ESP-IDF环境检查通过

REM 编译主体固件
echo.
echo ==========================================
echo 编译主体固件
echo ==========================================

cd 1_main_device_firmware
if errorlevel 1 (
    echo 错误: 无法进入主体固件目录
    pause
    exit /b 1
)

echo 设置目标芯片为ESP32-S3...
idf.py set-target esp32s3
if errorlevel 1 (
    echo 错误: 设置目标芯片失败
    cd ..
    pause
    exit /b 1
)

echo 开始编译主体固件...
idf.py build
if errorlevel 1 (
    echo 错误: 主体固件编译失败
    cd ..
    pause
    exit /b 1
)

echo ✓ 主体固件编译成功!

REM 显示主体固件信息
if exist "build\*.bin" (
    echo.
    echo 主体固件文件:
    dir build\*.bin
)

cd ..

REM 编译底座固件
echo.
echo ==========================================
echo 编译底座固件
echo ==========================================

cd 2_base_station_firmware
if errorlevel 1 (
    echo 错误: 无法进入底座固件目录
    pause
    exit /b 1
)

echo 设置目标芯片为ESP32-C2...
idf.py set-target esp32c2
if errorlevel 1 (
    echo 错误: 设置目标芯片失败
    cd ..
    pause
    exit /b 1
)

echo 开始编译底座固件...
idf.py build
if errorlevel 1 (
    echo 错误: 底座固件编译失败
    cd ..
    pause
    exit /b 1
)

echo ✓ 底座固件编译成功!

REM 显示底座固件信息
if exist "build\*.bin" (
    echo.
    echo 底座固件文件:
    dir build\*.bin
)

cd ..

REM 编译完成总结
echo.
echo ==========================================
echo ✓ 所有固件编译完成!
echo ==========================================
echo.
echo 固件位置:
echo   主体固件: 1_main_device_firmware\build\
echo   底座固件: 2_base_station_firmware\build\
echo.
echo 烧录命令:
echo   主体设备: cd 1_main_device_firmware ^&^& idf.py flash monitor
echo   底座设备: cd 2_base_station_firmware ^&^& idf.py flash monitor
echo.
echo ==========================================

pause
