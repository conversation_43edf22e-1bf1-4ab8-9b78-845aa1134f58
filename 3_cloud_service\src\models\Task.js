/**
 * 任务数据模型
 */

const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  // 基本信息
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // 所有者
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 任务状态
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'cancelled', 'overdue'],
    default: 'pending'
  },
  
  // 优先级
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // 任务类型
  type: {
    type: String,
    enum: ['general', 'work', 'study', 'life', 'health', 'entertainment'],
    default: 'general'
  },
  
  // 时间信息
  dueDate: {
    type: Date
  },
  reminderTime: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  
  // 重复设置
  recurring: {
    enabled: {
      type: Boolean,
      default: false
    },
    pattern: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly', 'custom'],
      default: 'daily'
    },
    interval: {
      type: Number,
      default: 1
    },
    daysOfWeek: [{
      type: Number,
      min: 0,
      max: 6
    }],
    endDate: {
      type: Date
    },
    maxOccurrences: {
      type: Number
    }
  },
  
  // 进度信息
  progress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  // 时间估算
  estimatedMinutes: {
    type: Number,
    min: 0
  },
  actualMinutes: {
    type: Number,
    min: 0,
    default: 0
  },
  
  // 标签
  tags: [{
    type: String,
    trim: true,
    maxlength: 20
  }],
  
  // 子任务
  subtasks: [{
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    completed: {
      type: Boolean,
      default: false
    },
    completedAt: {
      type: Date
    }
  }],
  
  // 附件
  attachments: [{
    filename: {
      type: String,
      required: true
    },
    originalName: {
      type: String,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 提醒设置
  reminder: {
    enabled: {
      type: Boolean,
      default: false
    },
    methods: [{
      type: String,
      enum: ['sound', 'vibration', 'light', 'notification']
    }],
    advanceMinutes: {
      type: Number,
      default: 15
    },
    repeat: {
      enabled: {
        type: Boolean,
        default: false
      },
      intervalMinutes: {
        type: Number,
        default: 5
      },
      maxRepeats: {
        type: Number,
        default: 3
      }
    }
  },
  
  // 番茄时钟相关
  pomodoro: {
    enabled: {
      type: Boolean,
      default: false
    },
    workDuration: {
      type: Number,
      default: 25
    },
    shortBreak: {
      type: Number,
      default: 5
    },
    longBreak: {
      type: Number,
      default: 15
    },
    completedSessions: {
      type: Number,
      default: 0
    },
    totalWorkTime: {
      type: Number,
      default: 0
    }
  },
  
  // 设备关联
  assignedDevices: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device'
  }],
  
  // 协作信息
  shared: {
    enabled: {
      type: Boolean,
      default: false
    },
    users: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      role: {
        type: String,
        enum: ['viewer', 'editor', 'owner'],
        default: 'viewer'
      },
      addedAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  
  // 统计信息
  stats: {
    viewCount: {
      type: Number,
      default: 0
    },
    editCount: {
      type: Number,
      default: 0
    },
    postponeCount: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// 索引
taskSchema.index({ owner: 1 });
taskSchema.index({ status: 1 });
taskSchema.index({ priority: 1 });
taskSchema.index({ type: 1 });
taskSchema.index({ dueDate: 1 });
taskSchema.index({ reminderTime: 1 });
taskSchema.index({ tags: 1 });
taskSchema.index({ createdAt: -1 });
taskSchema.index({ 'recurring.enabled': 1 });

// 虚拟字段
taskSchema.virtual('isOverdue').get(function() {
  return this.dueDate && this.dueDate < new Date() && this.status !== 'completed';
});

taskSchema.virtual('completionRate').get(function() {
  if (this.subtasks.length === 0) {
    return this.status === 'completed' ? 100 : 0;
  }
  const completedSubtasks = this.subtasks.filter(subtask => subtask.completed).length;
  return Math.round((completedSubtasks / this.subtasks.length) * 100);
});

taskSchema.virtual('timeSpent').get(function() {
  return this.actualMinutes || 0;
});

taskSchema.virtual('efficiency').get(function() {
  if (!this.estimatedMinutes || !this.actualMinutes) {
    return null;
  }
  return Math.round((this.estimatedMinutes / this.actualMinutes) * 100);
});

// 实例方法
taskSchema.methods.markCompleted = function() {
  this.status = 'completed';
  this.completedAt = new Date();
  this.progress = 100;
  return this.save();
};

taskSchema.methods.addSubtask = function(title) {
  this.subtasks.push({ title });
  return this.save();
};

taskSchema.methods.completeSubtask = function(subtaskId) {
  const subtask = this.subtasks.id(subtaskId);
  if (subtask) {
    subtask.completed = true;
    subtask.completedAt = new Date();
    
    // 更新总进度
    const completedCount = this.subtasks.filter(st => st.completed).length;
    this.progress = Math.round((completedCount / this.subtasks.length) * 100);
    
    // 如果所有子任务完成，标记主任务完成
    if (completedCount === this.subtasks.length) {
      this.markCompleted();
    }
  }
  return this.save();
};

taskSchema.methods.postpone = function(newDueDate) {
  this.dueDate = newDueDate;
  this.stats.postponeCount += 1;
  return this.save();
};

taskSchema.methods.addTimeSpent = function(minutes) {
  this.actualMinutes += minutes;
  return this.save();
};

// 静态方法
taskSchema.statics.findByOwner = function(ownerId) {
  return this.find({ owner: ownerId });
};

taskSchema.statics.findPending = function(ownerId) {
  return this.find({ 
    owner: ownerId, 
    status: { $in: ['pending', 'in_progress'] }
  });
};

taskSchema.statics.findOverdue = function(ownerId) {
  return this.find({
    owner: ownerId,
    dueDate: { $lt: new Date() },
    status: { $ne: 'completed' }
  });
};

taskSchema.statics.findByTag = function(ownerId, tag) {
  return this.find({
    owner: ownerId,
    tags: tag
  });
};

taskSchema.statics.findDueToday = function(ownerId) {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
  
  return this.find({
    owner: ownerId,
    dueDate: {
      $gte: startOfDay,
      $lt: endOfDay
    },
    status: { $ne: 'completed' }
  });
};

// 中间件
taskSchema.pre('save', function(next) {
  // 检查是否过期
  if (this.dueDate && this.dueDate < new Date() && this.status === 'pending') {
    this.status = 'overdue';
  }
  
  // 更新编辑计数
  if (!this.isNew && this.isModified()) {
    this.stats.editCount += 1;
  }
  
  next();
});

module.exports = mongoose.model('Task', taskSchema);
