/**
 * @file pet_emotion.h
 * @brief TIMO虚拟宠物情感系统头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef PET_EMOTION_H
#define PET_EMOTION_H

#include "esp_err.h"
#include "pet_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化宠物情感系统
 */
esp_err_t pet_emotion_init(void);

/**
 * @brief 反初始化宠物情感系统
 */
esp_err_t pet_emotion_deinit(void);

/**
 * @brief 计算宠物情绪
 */
pet_emotion_t pet_emotion_calculate(const pet_info_t *pet);

#ifdef __cplusplus
}
#endif

#endif // PET_EMOTION_H
