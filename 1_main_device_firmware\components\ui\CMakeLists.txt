idf_component_register(
    SRCS
        "ui_manager.c"
        "ui_theme.c"
        "ui_watchface.c"
        "ui_menu.c"
        "ui_settings.c"
        "ui_alarm.c"
        "ui_sensor.c"
        "ui_utils.c"
        "ui_voice.c"
        "ui_bluetooth.c"
        "ui_pet.c"
        "ui_task.c"
        "ui_pomodoro.c"
        "lvgl_port.c"
    INCLUDE_DIRS
        "include"
    REQUIRES
        hardware
        system
        voice_system
        driver
        esp_lcd
        esp_timer
        freertos
        log
        lvgl
)
