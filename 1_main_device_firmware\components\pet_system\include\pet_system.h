/**
 * @file pet_system.h
 * @brief TIMO虚拟宠物系统头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef PET_SYSTEM_H
#define PET_SYSTEM_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 宠物类型定义 */
typedef enum {
    PET_TYPE_CAT = 0,           // 小猫
    PET_TYPE_DOG,               // 小狗
    PET_TYPE_RABBIT,            // 小兔子
    PET_TYPE_BIRD,              // 小鸟
    PET_TYPE_DRAGON,            // 小龙
    PET_TYPE_MAX
} pet_type_t;

/* 宠物状态定义 */
typedef enum {
    PET_STATE_SLEEPING = 0,     // 睡觉
    PET_STATE_IDLE,             // 空闲
    PET_STATE_HAPPY,            // 开心
    PET_STATE_PLAYING,          // 玩耍
    PET_STATE_EATING,           // 吃东西
    PET_STATE_TALKING,          // 说话
    PET_STATE_THINKING,         // 思考
    PET_STATE_ANGRY,            // 生气
    PET_STATE_SAD,              // 伤心
    PET_STATE_SICK,             // 生病
    PET_STATE_MAX
} pet_state_t;

/* 宠物情绪定义 */
typedef enum {
    PET_EMOTION_NEUTRAL = 0,    // 中性
    PET_EMOTION_HAPPY,          // 开心
    PET_EMOTION_EXCITED,        // 兴奋
    PET_EMOTION_CALM,           // 平静
    PET_EMOTION_TIRED,          // 疲惫
    PET_EMOTION_ANGRY,          // 愤怒
    PET_EMOTION_SAD,            // 悲伤
    PET_EMOTION_LONELY,         // 孤独
    PET_EMOTION_CURIOUS,        // 好奇
    PET_EMOTION_PLAYFUL,        // 顽皮
    PET_EMOTION_MAX
} pet_emotion_t;

/* 宠物生命阶段 */
typedef enum {
    PET_STAGE_EGG = 0,          // 蛋
    PET_STAGE_BABY,             // 幼体
    PET_STAGE_CHILD,            // 儿童
    PET_STAGE_TEEN,             // 青少年
    PET_STAGE_ADULT,            // 成年
    PET_STAGE_ELDER,            // 老年
    PET_STAGE_MAX
} pet_stage_t;

/* 宠物属性 */
typedef struct {
    uint8_t health;             // 健康值 (0-100)
    uint8_t happiness;          // 快乐值 (0-100)
    uint8_t energy;             // 精力值 (0-100)
    uint8_t hunger;             // 饥饿值 (0-100)
    uint8_t cleanliness;        // 清洁度 (0-100)
    uint8_t intelligence;       // 智力值 (0-100)
    uint8_t affection;          // 亲密度 (0-100)
    uint32_t experience;        // 经验值
    uint32_t level;             // 等级
} pet_attributes_t;

/* 宠物信息 */
typedef struct {
    uint32_t id;                // 宠物ID
    char name[32];              // 宠物名称
    pet_type_t type;            // 宠物类型
    pet_stage_t stage;          // 生命阶段
    pet_state_t state;          // 当前状态
    pet_emotion_t emotion;      // 当前情绪
    pet_attributes_t attributes; // 属性
    time_t birth_time;          // 出生时间
    time_t last_interaction;    // 最后交互时间
    time_t last_feed;           // 最后喂食时间
    time_t last_clean;          // 最后清洁时间
    uint32_t total_interactions; // 总交互次数
    bool is_alive;              // 是否存活
} pet_info_t;

/* 宠物事件类型 */
typedef enum {
    PET_EVENT_BORN = 0,         // 出生
    PET_EVENT_GROW,             // 成长
    PET_EVENT_FEED,             // 喂食
    PET_EVENT_PLAY,             // 玩耍
    PET_EVENT_CLEAN,            // 清洁
    PET_EVENT_TALK,             // 对话
    PET_EVENT_SLEEP,            // 睡觉
    PET_EVENT_WAKE,             // 醒来
    PET_EVENT_SICK,             // 生病
    PET_EVENT_HEAL,             // 治愈
    PET_EVENT_LEVEL_UP,         // 升级
    PET_EVENT_EMOTION_CHANGE,   // 情绪变化
    PET_EVENT_MAX
} pet_event_type_t;

/* 宠物事件数据 */
typedef struct {
    pet_event_type_t type;      // 事件类型
    uint32_t pet_id;            // 宠物ID
    time_t timestamp;           // 时间戳
    union {
        pet_stage_t new_stage;  // 新阶段
        pet_emotion_t new_emotion; // 新情绪
        uint32_t new_level;     // 新等级
        uint8_t health_change;  // 健康变化
    } data;
} pet_event_t;

/* 宠物系统配置 */
typedef struct {
    bool auto_care;             // 自动照料
    uint32_t update_interval_ms; // 更新间隔
    uint8_t max_pets;           // 最大宠物数量
    bool sound_enabled;         // 声音开启
    bool animation_enabled;     // 动画开启
    uint8_t interaction_sensitivity; // 交互敏感度
} pet_config_t;

/* 宠物系统回调函数 */
typedef void (*pet_event_callback_t)(const pet_event_t *event);

/* 宠物系统API */

/**
 * @brief 初始化宠物系统
 */
esp_err_t pet_system_init(void);

/**
 * @brief 启动宠物系统
 */
esp_err_t pet_system_start(void);

/**
 * @brief 停止宠物系统
 */
esp_err_t pet_system_stop(void);

/**
 * @brief 反初始化宠物系统
 */
esp_err_t pet_system_deinit(void);

/**
 * @brief 设置宠物系统配置
 */
esp_err_t pet_system_set_config(const pet_config_t *config);

/**
 * @brief 获取宠物系统配置
 */
esp_err_t pet_system_get_config(pet_config_t *config);

/**
 * @brief 注册事件回调
 */
esp_err_t pet_system_register_event_callback(pet_event_callback_t callback);

/**
 * @brief 创建新宠物
 */
esp_err_t pet_system_create_pet(pet_type_t type, const char *name, uint32_t *pet_id);

/**
 * @brief 删除宠物
 */
esp_err_t pet_system_delete_pet(uint32_t pet_id);

/**
 * @brief 获取宠物信息
 */
esp_err_t pet_system_get_pet_info(uint32_t pet_id, pet_info_t *info);

/**
 * @brief 获取当前活跃宠物ID
 */
uint32_t pet_system_get_active_pet_id(void);

/**
 * @brief 设置活跃宠物
 */
esp_err_t pet_system_set_active_pet(uint32_t pet_id);

/**
 * @brief 喂食宠物
 */
esp_err_t pet_system_feed_pet(uint32_t pet_id);

/**
 * @brief 与宠物玩耍
 */
esp_err_t pet_system_play_with_pet(uint32_t pet_id);

/**
 * @brief 清洁宠物
 */
esp_err_t pet_system_clean_pet(uint32_t pet_id);

/**
 * @brief 与宠物对话
 */
esp_err_t pet_system_talk_to_pet(uint32_t pet_id, const char *message);

/**
 * @brief 抚摸宠物
 */
esp_err_t pet_system_pet_touch(uint32_t pet_id, uint16_t x, uint16_t y);

/**
 * @brief 检测手势交互
 */
esp_err_t pet_system_gesture_interaction(uint32_t pet_id, int gesture_type);

/**
 * @brief 获取宠物列表
 */
esp_err_t pet_system_get_pet_list(uint32_t *pet_ids, uint8_t *count, uint8_t max_count);

/**
 * @brief 保存宠物数据
 */
esp_err_t pet_system_save_data(void);

/**
 * @brief 加载宠物数据
 */
esp_err_t pet_system_load_data(void);

#ifdef __cplusplus
}
#endif

#endif // PET_SYSTEM_H
