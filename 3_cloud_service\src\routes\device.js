/**
 * 设备管理路由
 */

const express = require('express');
const Joi = require('joi');
const Device = require('../models/Device');
const User = require('../models/User');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const logger = require('../utils/logger');

const router = express.Router();

// 验证模式
const registerDeviceSchema = Joi.object({
  deviceId: Joi.string().required(),
  deviceType: Joi.string().valid('main', 'base').required(),
  name: Joi.string().max(50).required(),
  description: Joi.string().max(200).optional(),
  hardware: Joi.object({
    chipModel: Joi.string().required(),
    macAddress: Joi.string().required(),
    serialNumber: Joi.string().optional(),
    firmwareVersion: Joi.string().required(),
    hardwareVersion: Joi.string().optional()
  }).required(),
  location: Joi.object({
    room: Joi.string().optional(),
    coordinates: Joi.object({
      latitude: Joi.number().optional(),
      longitude: Joi.number().optional()
    }).optional()
  }).optional()
});

const updateDeviceSchema = Joi.object({
  name: Joi.string().max(50).optional(),
  description: Joi.string().max(200).optional(),
  config: Joi.object({
    timezone: Joi.string().optional(),
    language: Joi.string().optional(),
    volume: Joi.number().min(0).max(100).optional(),
    brightness: Joi.number().min(0).max(100).optional(),
    autoSleep: Joi.object({
      enabled: Joi.boolean().optional(),
      startTime: Joi.string().optional(),
      endTime: Joi.string().optional()
    }).optional(),
    voiceSettings: Joi.object({
      engine: Joi.string().valid('local', 'esp-sr', 'cloud', 'hybrid').optional(),
      wakeWord: Joi.string().optional(),
      sensitivity: Joi.number().min(0).max(1).optional()
    }).optional()
  }).optional(),
  sensors: Joi.object({
    temperature: Joi.object({
      enabled: Joi.boolean().optional(),
      interval: Joi.number().optional(),
      thresholds: Joi.object({
        min: Joi.number().optional(),
        max: Joi.number().optional()
      }).optional()
    }).optional(),
    humidity: Joi.object({
      enabled: Joi.boolean().optional(),
      interval: Joi.number().optional(),
      thresholds: Joi.object({
        min: Joi.number().optional(),
        max: Joi.number().optional()
      }).optional()
    }).optional(),
    co2: Joi.object({
      enabled: Joi.boolean().optional(),
      interval: Joi.number().optional(),
      threshold: Joi.number().optional()
    }).optional(),
    light: Joi.object({
      enabled: Joi.boolean().optional(),
      interval: Joi.number().optional(),
      autoBrightness: Joi.boolean().optional()
    }).optional()
  }).optional(),
  location: Joi.object({
    room: Joi.string().optional(),
    coordinates: Joi.object({
      latitude: Joi.number().optional(),
      longitude: Joi.number().optional()
    }).optional()
  }).optional()
});

// 设备注册
router.post('/register', auth, validate(registerDeviceSchema), async (req, res) => {
  try {
    const { deviceId, deviceType, name, description, hardware, location } = req.body;
    
    // 检查设备是否已存在
    const existingDevice = await Device.findOne({
      $or: [
        { deviceId },
        { 'hardware.macAddress': hardware.macAddress }
      ]
    });
    
    if (existingDevice) {
      return res.status(400).json({
        error: 'Device already exists',
        message: 'Device ID or MAC address already registered'
      });
    }
    
    // 检查用户设备数量限制
    const userDeviceCount = await Device.countDocuments({
      owner: req.userId,
      isActive: true
    });
    
    const maxDevices = process.env.MAX_DEVICES_PER_USER || 10;
    if (userDeviceCount >= maxDevices) {
      return res.status(400).json({
        error: 'Device limit exceeded',
        message: `Maximum ${maxDevices} devices allowed per user`
      });
    }
    
    // 创建新设备
    const device = new Device({
      deviceId,
      deviceType,
      name,
      description,
      owner: req.userId,
      hardware,
      location
    });
    
    // 生成认证Token
    await device.generateAuthToken();
    await device.save();
    
    // 更新用户设备统计
    await User.findByIdAndUpdate(req.userId, {
      $inc: { 'stats.devicesCount': 1 }
    });
    
    logger.info(`Device registered: ${deviceId} by user ${req.userId}`);
    
    res.status(201).json({
      message: 'Device registered successfully',
      device: {
        id: device._id,
        deviceId: device.deviceId,
        deviceType: device.deviceType,
        name: device.name,
        status: device.status,
        authToken: device.authToken,
        registrationDate: device.registrationDate
      }
    });
  } catch (error) {
    logger.error('Device registration error:', error);
    res.status(500).json({
      error: 'Device registration failed',
      message: error.message
    });
  }
});

// 获取用户设备列表
router.get('/', auth, async (req, res) => {
  try {
    const { type, status } = req.query;
    
    const filter = {
      owner: req.userId,
      isActive: true
    };
    
    if (type) {
      filter.deviceType = type;
    }
    
    if (status) {
      filter.status = status;
    }
    
    const devices = await Device.find(filter)
      .populate('pairedDevice', 'deviceId name deviceType')
      .sort({ createdAt: -1 });
    
    res.json({
      devices: devices.map(device => ({
        id: device._id,
        deviceId: device.deviceId,
        deviceType: device.deviceType,
        name: device.name,
        description: device.description,
        status: device.status,
        isOnline: device.isOnline,
        hardware: device.hardware,
        network: device.network,
        config: device.config,
        location: device.location,
        pairedDevice: device.pairedDevice,
        stats: device.stats,
        registrationDate: device.registrationDate,
        lastSeen: device.network.lastSeen
      }))
    });
  } catch (error) {
    logger.error('Get devices error:', error);
    res.status(500).json({
      error: 'Failed to get devices',
      message: error.message
    });
  }
});

// 获取单个设备信息
router.get('/:deviceId', auth, async (req, res) => {
  try {
    const device = await Device.findOne({
      deviceId: req.params.deviceId,
      owner: req.userId,
      isActive: true
    }).populate('pairedDevice', 'deviceId name deviceType');
    
    if (!device) {
      return res.status(404).json({
        error: 'Device not found'
      });
    }
    
    res.json({
      device: {
        id: device._id,
        deviceId: device.deviceId,
        deviceType: device.deviceType,
        name: device.name,
        description: device.description,
        status: device.status,
        isOnline: device.isOnline,
        hardware: device.hardware,
        network: device.network,
        config: device.config,
        sensors: device.sensors,
        location: device.location,
        pairedDevice: device.pairedDevice,
        stats: device.stats,
        registrationDate: device.registrationDate,
        lastSeen: device.network.lastSeen,
        uptimeHours: device.uptimeHours
      }
    });
  } catch (error) {
    logger.error('Get device error:', error);
    res.status(500).json({
      error: 'Failed to get device',
      message: error.message
    });
  }
});

// 更新设备信息
router.put('/:deviceId', auth, validate(updateDeviceSchema), async (req, res) => {
  try {
    const device = await Device.findOne({
      deviceId: req.params.deviceId,
      owner: req.userId,
      isActive: true
    });
    
    if (!device) {
      return res.status(404).json({
        error: 'Device not found'
      });
    }
    
    // 更新设备信息
    const { name, description, config, sensors, location } = req.body;
    
    if (name) device.name = name;
    if (description) device.description = description;
    if (config) device.config = { ...device.config, ...config };
    if (sensors) device.sensors = { ...device.sensors, ...sensors };
    if (location) device.location = { ...device.location, ...location };
    
    await device.save();
    
    logger.info(`Device updated: ${req.params.deviceId} by user ${req.userId}`);
    
    res.json({
      message: 'Device updated successfully',
      device: {
        id: device._id,
        deviceId: device.deviceId,
        name: device.name,
        description: device.description,
        config: device.config,
        sensors: device.sensors,
        location: device.location
      }
    });
  } catch (error) {
    logger.error('Update device error:', error);
    res.status(500).json({
      error: 'Failed to update device',
      message: error.message
    });
  }
});

// 设备心跳
router.post('/:deviceId/heartbeat', async (req, res) => {
  try {
    const { authToken, status, network } = req.body;
    
    const device = await Device.findOne({
      deviceId: req.params.deviceId,
      authToken
    });
    
    if (!device || !device.isTokenValid()) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid device token'
      });
    }
    
    // 更新设备状态
    device.status = status || 'online';
    device.network.lastSeen = new Date();
    
    if (network) {
      device.network = { ...device.network, ...network };
    }
    
    await device.save();
    
    res.json({
      message: 'Heartbeat received',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Device heartbeat error:', error);
    res.status(500).json({
      error: 'Heartbeat failed',
      message: error.message
    });
  }
});

// 设备配对
router.post('/:deviceId/pair', auth, async (req, res) => {
  try {
    const { targetDeviceId } = req.body;
    
    const device = await Device.findOne({
      deviceId: req.params.deviceId,
      owner: req.userId,
      isActive: true
    });
    
    const targetDevice = await Device.findOne({
      deviceId: targetDeviceId,
      owner: req.userId,
      isActive: true
    });
    
    if (!device || !targetDevice) {
      return res.status(404).json({
        error: 'Device not found'
      });
    }
    
    if (device.deviceType === targetDevice.deviceType) {
      return res.status(400).json({
        error: 'Invalid pairing',
        message: 'Cannot pair devices of the same type'
      });
    }
    
    // 设置配对关系
    device.pairedDevice = targetDevice._id;
    targetDevice.pairedDevice = device._id;
    
    await device.save();
    await targetDevice.save();
    
    logger.info(`Devices paired: ${req.params.deviceId} <-> ${targetDeviceId}`);
    
    res.json({
      message: 'Devices paired successfully',
      pairing: {
        device1: {
          deviceId: device.deviceId,
          name: device.name,
          type: device.deviceType
        },
        device2: {
          deviceId: targetDevice.deviceId,
          name: targetDevice.name,
          type: targetDevice.deviceType
        }
      }
    });
  } catch (error) {
    logger.error('Device pairing error:', error);
    res.status(500).json({
      error: 'Device pairing failed',
      message: error.message
    });
  }
});

// 删除设备
router.delete('/:deviceId', auth, async (req, res) => {
  try {
    const device = await Device.findOne({
      deviceId: req.params.deviceId,
      owner: req.userId,
      isActive: true
    });
    
    if (!device) {
      return res.status(404).json({
        error: 'Device not found'
      });
    }
    
    // 软删除设备
    device.isActive = false;
    await device.save();
    
    // 更新用户设备统计
    await User.findByIdAndUpdate(req.userId, {
      $inc: { 'stats.devicesCount': -1 }
    });
    
    logger.info(`Device deleted: ${req.params.deviceId} by user ${req.userId}`);
    
    res.json({
      message: 'Device deleted successfully'
    });
  } catch (error) {
    logger.error('Delete device error:', error);
    res.status(500).json({
      error: 'Failed to delete device',
      message: error.message
    });
  }
});

module.exports = router;
