clean_set:
  wake_words_paths:
      # - "/home/<USER>/pipeline-test/hiesp"
    - "/home/<USER>/pipeline-test/hilexin"
  commands_paths:
      # - "/home/<USER>/pipeline-test/clean_norm"
    - "/home/<USER>/pipeline-test/CN-TEST-S"
  filelists_paths:
      # - "/home/<USER>/pipeline-test/en_test.json"
    - "/home/<USER>/pipeline-test/cn_test.json"
  normalization: True
  target_dB: -36
noise_set:
  paths:
    - "/home/<USER>/_noise_korvo"
  normalization: True
  target_dB: -36
output_set:
  path: "/home/<USER>/pipeline-test/test"
  remove_old_files: true
  snr:
    - snr_dB: 10
      clean_gain_dB: 0
    - snr_dB: 5
      clean_gain_dB: 0
    - snr_dB: 0
      clean_gain_dB: 5
player:
  play_output: true
