/**
 * @file ui_pomodoro.h
 * @brief TIMO番茄时钟UI界面头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef UI_POMODORO_H
#define UI_POMODORO_H

#include "esp_err.h"
#include "task_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化番茄时钟UI
 */
esp_err_t ui_pomodoro_init(void);

/**
 * @brief 反初始化番茄时钟UI
 */
esp_err_t ui_pomodoro_deinit(void);

/**
 * @brief 显示番茄时钟页面
 */
void ui_pomodoro_show_page(void);

/**
 * @brief 隐藏番茄时钟页面
 */
void ui_pomodoro_hide_page(void);

/**
 * @brief 更新番茄时钟显示
 */
void ui_pomodoro_update_display(const pomodoro_info_t *info);

/**
 * @brief 显示番茄时钟设置
 */
void ui_pomodoro_show_settings(void);

#ifdef __cplusplus
}
#endif

#endif // UI_POMODORO_H
