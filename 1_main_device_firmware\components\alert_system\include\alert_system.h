/**
 * @file alert_system.h
 * @brief TIMO环境监测预警系统头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef ALERT_SYSTEM_H
#define ALERT_SYSTEM_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 预警级别定义 */
typedef enum {
    ALERT_LEVEL_NONE = 0,       // 无预警
    ALERT_LEVEL_INFO,           // 信息
    ALERT_LEVEL_LOW,            // 低级预警
    ALERT_LEVEL_MEDIUM,         // 中级预警
    ALERT_LEVEL_HIGH,           // 高级预警
    ALERT_LEVEL_CRITICAL,       // 严重预警
    ALERT_LEVEL_MAX
} alert_level_t;

/* 预警类型定义 */
typedef enum {
    ALERT_TYPE_TEMPERATURE = 0, // 温度预警
    ALERT_TYPE_HUMIDITY,        // 湿度预警
    ALERT_TYPE_CO2,             // CO2预警
    ALERT_TYPE_LIGHT,           // 光照预警
    ALERT_TYPE_AIR_QUALITY,     // 空气质量预警
    ALERT_TYPE_COMFORT,         // 舒适度预警
    ALERT_TYPE_HEALTH,          // 健康预警
    ALERT_TYPE_SYSTEM,          // 系统预警
    ALERT_TYPE_MAX
} alert_type_t;

/* 预警方式定义 */
typedef enum {
    ALERT_METHOD_NONE = 0,      // 无预警
    ALERT_METHOD_SOUND = 1,     // 声音预警
    ALERT_METHOD_LIGHT = 2,     // 灯光预警
    ALERT_METHOD_VIBRATION = 4, // 振动预警
    ALERT_METHOD_NOTIFICATION = 8, // 通知预警
    ALERT_METHOD_ALL = 15       // 全部预警
} alert_method_t;

/* 环境阈值配置 */
typedef struct {
    float temp_min;             // 最低温度
    float temp_max;             // 最高温度
    float humidity_min;         // 最低湿度
    float humidity_max;         // 最高湿度
    uint16_t co2_max;           // 最高CO2浓度
    float light_min;            // 最低光照
    float light_max;            // 最高光照
    uint16_t aqi_max;           // 最高空气质量指数
    uint8_t comfort_min;        // 最低舒适度
} environment_thresholds_t;

/* 预警规则 */
typedef struct {
    alert_type_t type;          // 预警类型
    alert_level_t level;        // 预警级别
    alert_method_t method;      // 预警方式
    bool enabled;               // 是否启用
    uint32_t duration_s;        // 持续时间(秒)
    uint32_t interval_s;        // 重复间隔(秒)
    uint32_t max_repeats;       // 最大重复次数
    char message[128];          // 预警消息
    char sound_file[64];        // 声音文件
    uint32_t light_color;       // 灯光颜色
    uint8_t light_brightness;   // 灯光亮度
} alert_rule_t;

/* 预警信息 */
typedef struct {
    uint32_t id;                // 预警ID
    alert_type_t type;          // 预警类型
    alert_level_t level;        // 预警级别
    time_t timestamp;           // 时间戳
    time_t resolved_time;       // 解决时间
    bool is_active;             // 是否活跃
    bool is_acknowledged;       // 是否已确认
    char message[128];          // 预警消息
    float trigger_value;        // 触发值
    float threshold_value;      // 阈值
    uint32_t repeat_count;      // 重复次数
} alert_info_t;

/* 预警统计信息 */
typedef struct {
    uint32_t total_alerts;      // 总预警数
    uint32_t active_alerts;     // 活跃预警数
    uint32_t resolved_alerts;   // 已解决预警数
    uint32_t critical_alerts;   // 严重预警数
    uint32_t alerts_today;      // 今日预警数
    time_t last_alert_time;     // 最后预警时间
    alert_type_t most_frequent_type; // 最频繁预警类型
} alert_statistics_t;

/* 预警系统配置 */
typedef struct {
    bool enabled;               // 系统启用
    uint32_t check_interval_s;  // 检查间隔(秒)
    bool auto_resolve;          // 自动解决
    uint32_t auto_resolve_delay_s; // 自动解决延迟(秒)
    bool sound_enabled;         // 声音启用
    bool light_enabled;         // 灯光启用
    bool notification_enabled;  // 通知启用
    uint8_t default_volume;     // 默认音量
    environment_thresholds_t thresholds; // 环境阈值
    alert_rule_t rules[ALERT_TYPE_MAX]; // 预警规则
} alert_system_config_t;

/* 预警事件类型 */
typedef enum {
    ALERT_EVENT_TRIGGERED = 0,  // 预警触发
    ALERT_EVENT_RESOLVED,       // 预警解决
    ALERT_EVENT_ACKNOWLEDGED,   // 预警确认
    ALERT_EVENT_ESCALATED,      // 预警升级
    ALERT_EVENT_REPEATED,       // 预警重复
    ALERT_EVENT_MAX
} alert_event_type_t;

/* 预警事件数据 */
typedef struct {
    alert_event_type_t event_type; // 事件类型
    uint32_t alert_id;          // 预警ID
    alert_type_t alert_type;    // 预警类型
    alert_level_t alert_level;  // 预警级别
    time_t timestamp;           // 时间戳
    float value;                // 相关数值
} alert_event_t;

/* 预警系统回调函数 */
typedef void (*alert_event_callback_t)(const alert_event_t *event);

/* 预警系统API */

/**
 * @brief 初始化预警系统
 */
esp_err_t alert_system_init(void);

/**
 * @brief 启动预警系统
 */
esp_err_t alert_system_start(void);

/**
 * @brief 停止预警系统
 */
esp_err_t alert_system_stop(void);

/**
 * @brief 反初始化预警系统
 */
esp_err_t alert_system_deinit(void);

/**
 * @brief 设置预警系统配置
 */
esp_err_t alert_system_set_config(const alert_system_config_t *config);

/**
 * @brief 获取预警系统配置
 */
esp_err_t alert_system_get_config(alert_system_config_t *config);

/**
 * @brief 注册事件回调
 */
esp_err_t alert_system_register_event_callback(alert_event_callback_t callback);

/**
 * @brief 设置环境阈值
 */
esp_err_t alert_system_set_thresholds(const environment_thresholds_t *thresholds);

/**
 * @brief 获取环境阈值
 */
esp_err_t alert_system_get_thresholds(environment_thresholds_t *thresholds);

/**
 * @brief 设置预警规则
 */
esp_err_t alert_system_set_rule(alert_type_t type, const alert_rule_t *rule);

/**
 * @brief 获取预警规则
 */
esp_err_t alert_system_get_rule(alert_type_t type, alert_rule_t *rule);

/**
 * @brief 手动触发预警
 */
esp_err_t alert_system_trigger_alert(alert_type_t type, alert_level_t level, 
                                    const char *message, float value);

/**
 * @brief 确认预警
 */
esp_err_t alert_system_acknowledge_alert(uint32_t alert_id);

/**
 * @brief 解决预警
 */
esp_err_t alert_system_resolve_alert(uint32_t alert_id);

/**
 * @brief 获取活跃预警列表
 */
esp_err_t alert_system_get_active_alerts(alert_info_t *alerts, uint32_t *count, uint32_t max_count);

/**
 * @brief 获取预警历史
 */
esp_err_t alert_system_get_alert_history(alert_info_t *alerts, uint32_t *count, 
                                        uint32_t max_count, time_t start_time, time_t end_time);

/**
 * @brief 获取预警统计信息
 */
esp_err_t alert_system_get_statistics(alert_statistics_t *stats);

/**
 * @brief 清除预警历史
 */
esp_err_t alert_system_clear_history(time_t before_time);

/**
 * @brief 测试预警
 */
esp_err_t alert_system_test_alert(alert_type_t type, alert_level_t level);

/**
 * @brief 静音预警
 */
esp_err_t alert_system_mute(uint32_t duration_s);

/**
 * @brief 取消静音
 */
esp_err_t alert_system_unmute(void);

/**
 * @brief 检查是否静音
 */
bool alert_system_is_muted(void);

/**
 * @brief 保存预警数据
 */
esp_err_t alert_system_save_data(void);

/**
 * @brief 加载预警数据
 */
esp_err_t alert_system_load_data(void);

#ifdef __cplusplus
}
#endif

#endif // ALERT_SYSTEM_H
