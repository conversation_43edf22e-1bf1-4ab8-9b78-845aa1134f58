CONFIG_IDF_TARGET="esp32s3"
CONFIG_IDF_TARGET_ESP32S3=y
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_ESP32_S3_KORVO_2_V3_0_BOARD=y
CONFIG_ESP32S3_DEFAULT_CPU_FREQ_240=y
CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB=y
CONFIG_ESP32S3_DATA_CACHE_64KB=y
CONFIG_ESP32S3_DATA_CACHE_LINE_64B=y
CONFIG_ESP32S3_SPIRAM_SUPPORT=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y
# CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT is not set
CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT=y
# CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT is not set
# CONFIG_ESP_SYSTEM_PANIC_GDBSTUB is not set
# CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME is not set
CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK=y
CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP=y
CONFIG_ESP_MAIN_TASK_STACK_SIZE=5000
CONFIG_FATFS_LFN_HEAP=y

CONFIG_MODEL_IN_SPIFFS=y
# CONFIG_MODEL_IN_SDCARD is not set
CONFIG_USE_WAKENET=y
# CONFIG_SR_WN_WN9_HILEXIN is not set
# CONFIG_SR_WN_WN9_XIAOAITONGXUE is not set
# CONFIG_SR_WN_WN9_ALEXA is not set
CONFIG_SR_WN_WN9_HIESP=y
# CONFIG_SR_WN_WN9_NIHAOXIAOZHI is not set
# CONFIG_SR_WN_WN9_CUSTOMWORD is not set
# CONFIG_SR_WN_LOAD_MULIT_WORD is not set
CONFIG_USE_MULTINET=y
CONFIG_SR_MN_CN_NONE=y
# CONFIG_SR_MN_CN_MULTINET6_QUANT is not set
# CONFIG_SR_MN_CN_MULTINET6_AC_QUANT is not set
# CONFIG_SR_MN_EN_NONE is not set
CONFIG_SR_MN_EN_MULTINET6_QUANT=y
# end of ESP Speech Recognition