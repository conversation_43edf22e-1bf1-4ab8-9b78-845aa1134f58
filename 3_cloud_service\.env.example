# TIMO云端服务环境配置示例

# 应用配置
NODE_ENV=development
PORT=3000
APP_NAME=TIMO Cloud Service
APP_VERSION=1.0.0

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/timo
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 语音服务配置
# 百度语音服务
BAIDU_ASR_API_KEY=your-baidu-asr-api-key
BAIDU_ASR_SECRET_KEY=your-baidu-asr-secret-key
BAIDU_TTS_API_KEY=your-baidu-tts-api-key
BAIDU_TTS_SECRET_KEY=your-baidu-tts-secret-key

# 讯飞语音服务
XFYUN_APP_ID=your-xfyun-app-id
XFYUN_API_KEY=your-xfyun-api-key
XFYUN_API_SECRET=your-xfyun-api-secret

# 大语言模型配置
# OpenAI
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 通义千问
QWEN_API_KEY=your-qwen-api-key
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# 文心一言
ERNIE_API_KEY=your-ernie-api-key
ERNIE_SECRET_KEY=your-ernie-secret-key

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=TIMO智能闹钟 <<EMAIL>>

# 短信服务配置
SMS_PROVIDER=aliyun
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
SMS_SIGN_NAME=TIMO智能闹钟
SMS_TEMPLATE_CODE=SMS_123456789

# 微信服务配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret
WECHAT_TOKEN=your-wechat-token
WECHAT_ENCODING_AES_KEY=your-wechat-encoding-aes-key

# 天气服务配置
WEATHER_API_KEY=your-weather-api-key
WEATHER_BASE_URL=https://api.openweathermap.org/data/2.5

# 文件存储配置
# 本地存储
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 阿里云OSS
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your-oss-access-key-id
OSS_ACCESS_KEY_SECRET=your-oss-access-key-secret
OSS_BUCKET=timo-files

# 腾讯云COS
COS_SECRET_ID=your-cos-secret-id
COS_SECRET_KEY=your-cos-secret-key
COS_REGION=ap-beijing
COS_BUCKET=timo-files-**********

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=*
FRONTEND_URL=http://localhost:8080

# MQTT配置
MQTT_BROKER_URL=mqtt://localhost:1883
MQTT_USERNAME=timo
MQTT_PASSWORD=timo123
MQTT_CLIENT_ID=timo-cloud-service

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# 设备配置
MAX_DEVICES_PER_USER=10
DEVICE_HEARTBEAT_INTERVAL=60000
DEVICE_OFFLINE_TIMEOUT=300000

# 主题配置
THEME_UPLOAD_PATH=./uploads/themes
MAX_THEME_SIZE=5242880
ALLOWED_THEME_TYPES=.zip,.tar.gz

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups
