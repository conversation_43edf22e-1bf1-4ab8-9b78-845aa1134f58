/**
 * @file ui_task.h
 * @brief TIMO任务管理UI界面头文件
 * @version 1.0.0
 * @date 2025-06-30
 */

#ifndef UI_TASK_H
#define UI_TASK_H

#include "esp_err.h"
#include "task_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化任务UI
 */
esp_err_t ui_task_init(void);

/**
 * @brief 反初始化任务UI
 */
esp_err_t ui_task_deinit(void);

/**
 * @brief 显示任务页面
 */
void ui_task_show_page(void);

/**
 * @brief 隐藏任务页面
 */
void ui_task_hide_page(void);

/**
 * @brief 更新任务列表显示
 */
void ui_task_update_list(void);

/**
 * @brief 显示任务创建对话框
 */
void ui_task_show_create_dialog(void);

/**
 * @brief 显示任务详情
 */
void ui_task_show_details(uint32_t task_id);

#ifdef __cplusplus
}
#endif

#endif // UI_TASK_H
